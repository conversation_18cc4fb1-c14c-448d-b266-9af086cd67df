package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.PI


/**
 *
 * <AUTHOR>
 */

object Orders {

    /**
     * Determines the direction of a point relative to a root point along a given parallel vector.
     *
     * @param parallel The parallel vector defining the direction.
     * @param root The root point.
     * @param p The point to determine the direction for.
     * @return -1 if the point is in the direction of the vector, 1 otherwise.
     */
    fun directionOfPointOnParallelVector(parallel: Vector3D, root: Vector3D, p: Vector3D): Int {
        val vecUnit = parallel.normalize()
        val k1 = root.dot(vecUnit)
        val k2 = p.dot(vecUnit)
        return if (k1 < k2) -1 else 1
    }

    /**
     * Sorts points on a line based on a parallel vector.
     *
     * This function assumes all points lie on the line defined by the parallel vector.  It does
     * not perform any checks to ensure this condition is met.
     *
     * @param parallel The vector parallel to the line.
     * @param points A list of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted along the line.
     */
    fun pointsOnParallelVector(parallel: Vector3D, points: List<Vector3D>): List<Vector3D> {
        if (points.size < 2) return points

        val vecUnit = parallel.normalize()
        return points.sortedBy { it.dot(vecUnit) }
    }

    /**
     * Sorts points on a line based on a parallel vector.
     *
     * This function assumes all points lie on the line defined by the parallel vector. It does
     * not perform any checks to ensure this condition is met.
     *
     * @param parallel The vector parallel to the line.
     * @param points Vararg of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted along the line.
     */
    fun pointsOnParallelVector(parallel: Vector3D, vararg points: Vector3D): List<Vector3D> {
        return pointsOnParallelVector(parallel, points.toList())
    }

    /**
     * Sorts points on a circle based on their angle relative to a reference point.
     *
     * This function assumes all points lie on the circle.  It does not check this condition.
     *
     * @param refCircle The circle on which the points lie.
     * @param refPoint A point on the circle used as a reference for angle calculation.
     * @param points A list of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle on the circle.
     */
    fun pointsOnCircle(refCircle: Circle, refPoint: Vector3D, points: List<Vector3D>): List<Vector3D> {
        if (points.size < 2) return points

        val doc = refCircle.doc

        val center = refCircle.centerPoint.coordinates()
        val p = PointImpl(doc, null, refPoint)
        val line = LineSegmentImpl(doc, null, refCircle.centerPoint, p)
        val ref = Intersections.of(line, refCircle)!!.first { Points.isBetweenTwoPoints(it, center, refPoint) }

        val angleRef = Circles.angleOfPoint(center, refCircle.radius, ref)

        return points.sortedBy {
            var angle = Circles.angleOfPoint(center, refCircle.radius, it)
            if (angle >= angleRef) angle = angle - angleRef
            else angle = 2 * PI - angleRef + angle
            angle
        }
    }

    /**
     * Sorts points on a circle based on their angle relative to a reference point.
     *
     * This function assumes all points lie on the circle. It does not check this condition.
     *
     * @param refCircle The circle on which the points lie.
     * @param refPoint A point on the circle used as a reference for angle calculation.
     * @param points Vararg of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle on the circle.
     */
    fun pointsOnCircle(refCircle: Circle, refPoint: Vector3D, vararg points: Vector3D): List<Vector3D> {
        return pointsOnCircle(refCircle, refPoint, points.toList())
    }

    /**
     * Sorts points on an ellipse based on their angle relative to the vector from the center to the second focus (f2).
     *
     * This method assumes that all points lie on the Ellipse, but does not explicitly check.
     *
     * @param refEllipse The ellipse on which the points lie.
     * @param points A list of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle on the ellipse.
     */
    fun pointsOnEllipse(refEllipse: Ellipse, points: List<Vector3D>): List<Vector3D> {
        if (points.size < 2) return points
        val center = refEllipse.center
        val f2 = refEllipse.f2.coordinates()
        val vecCF2 = center.vectorTo(f2)
        return points.sortedBy {
            val vec = center.vectorTo(it)
            vecCF2.angleTo(vec)
        }
    }

    /**
     * Sorts points on an ellipse based on their angle relative to the vector from the center to the second focus (f2).
     *
     * This method assumes that all points lie on the Ellipse, but does not explicitly check.
     *
     * @param refEllipse The ellipse on which the points lie.
     * @param points Vararg of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle on the ellipse.
     */
    fun pointsOnEllipse(refEllipse: Ellipse, vararg points: Vector3D): List<Vector3D> {
        return pointsOnEllipse(refEllipse, points.toList())
    }

    /**
     * Sorts points based on their angle relative to a reference vector and a "role" vector.
     *
     * @param vec The reference vector.
     * @param role The "role" vector, potentially defining an origin or orientation.
     * @param points Vararg of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle.
     */
    fun pointsByRotation(vec: Vector3D, role: Vector3D, vararg points: Vector3D): List<Vector3D> {
        return pointsByRotation(vec, role, points.toList())
    }

    /**
     * Sorts points based on their angle relative to a reference vector and a "role" vector.
     *
     * @param vec The reference vector.
     * @param role The "role" vector, potentially defining an origin or orientation.
     * @param points A list of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle.
     */
    fun pointsByRotation(vec: Vector3D, role: Vector3D, points: List<Vector3D>): List<Vector3D> {
        return points.sortedBy { vec.angleTo(role.vectorTo(it)) }
    }

    /**
     * Orders two points based on their angle relative to a parallel vector and a "role" vector.
     *
     * This function appears designed to order points that could be mirror images across a line, using
     * angles to disambiguate their order.  If the points are identical, they are returned in a list.
     * If the angles formed by the points with the parallel and role vectors place them on opposite
     * sides of PI radians, they are returned in a specific order. Otherwise, an empty list is returned,
     * implying the ordering cannot be determined by this method.
     *
     * @param parallel The parallel vector defining the main direction.
     * @param role The "role" vector providing a reference point or orientation.
     * @param v1 The first point.
     * @param v2 The second point.
     * @return A list containing v1 and v2 in a determined order, or an empty list if the order is ambiguous.
     */
    fun pointByParallelVector(parallel: Vector3D, role: Vector3D, v1: Vector3D, v2: Vector3D): List<Vector3D> {
        if (v1 == v2) return listOf(v1, v2)
        val angle1 = parallel.angleTo(role.vectorTo(v1))
        val angle2 = parallel.angleTo(role.vectorTo(v2))
        if (angle1 < PI && angle2 > PI) return listOf(v1, v2)
        if (angle2 < PI && angle1 > PI) return listOf(v2, v1)
        return emptyList()
    }

    /**
     * Orders a list of `Point` elements based on their creation order within a `GeoDoc`.
     *
     * It utilizes the `getIndex` method of the `GeoDoc` to determine the creation order.
     *
     * @param doc The `GeoDoc` containing the points.
     * @param points Vararg of `Point` elements to be sorted.
     * @return A list of `Point` elements sorted by their creation order.
     */
    fun pointByCreateTime(doc: GeoDoc, vararg points: Point): List<Point> {
        return points.sortedBy { doc.getIndex(it) }
    }

    /**
     * Orders two points based on their counter-clockwise angle relative to a reference line.
     *
     * This function sorts two points, v1 and v2, based on the counter-clockwise angle formed by
     * rotating from the reference line's direction vector to the vector pointing from an
     * arbitrary point on the line to each of the input points.
     *
     * If the points are identical, they are returned in a list. If the points are collinear
     * with the reference line or the ordering cannot be determined unambiguously, an empty
     * list is returned.
     *
     * @param refLine The reference line.
     * @param v1 The first point.
     * @param v2 The second point.
     * @return A list containing v1 and v2 in counter-clockwise order, or an empty list if ambiguous.
     */
    fun pointsBaseLineReference(refLine: LineVi, v1: Vector3D, v2: Vector3D): List<Vector3D> {
        if (v1 == v2) return listOf(v1, v2)

        // Calculate the angle from the line's direction vector to the vector from
        // an arbitrary point on the line (here, p1) to each of the input points.
        val p1OnLine = refLine.p1.coordinates()
        val refVector = refLine.parallelVector

        val angle1 = refVector.angleTo(p1OnLine.vectorTo(v1))
        val angle2 = refVector.angleTo(p1OnLine.vectorTo(v2))

        // Order points by increasing counter-clockwise angle.
        return if (angle1 < angle2) {
            listOf(v1, v2)
        } else if (angle1 > angle2) {
            listOf(v2, v1)
        } else {
            // Points are collinear with the reference line, or ordering is ambiguous.
            emptyList()
        }
    }

    /**
     * Orders points in the document coordinate system from left to right, then top to bottom.
     *
     * This overloaded function sorts a list of Vector3D points based on their x and y coordinates.
     * Points are primarily ordered by their x-coordinate (leftmost first), and points with the same
     * x-coordinate are further ordered by their y-coordinate (topmost first).
     *
     * @param points A list of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their document coordinates.
     */
    @JvmName("pointsInDocumentVector3D")
    fun pointsInDocument(points: List<Vector3D>): List<Vector3D> {
        return points.sortedWith { o1, o2 ->
            if (o1.x < o2.x) 1
            else if (o1.x > o2.x) -1
            else if (o1.y < o2.y) 1
            else if (o1.y > o2.y) -1
            else 0
        }
    }

    /**
     * Orders points in the document coordinate system from left to right, then top to bottom.
     *
     * This overloaded function sorts a list of Point objects based on their x and y coordinates.
     * Points are primarily ordered by their x-coordinate (leftmost first), and points with the same
     * x-coordinate are further ordered by their y-coordinate (topmost first).
     *
     * @param points A list of Point objects to sort.
     * @return A list of Point objects sorted by their document coordinates.
     */
    @JvmName("pointsInDocumentPoint")
    fun pointsInDocument(points: List<Point>): List<Point> {
        return points.sortedWith { o1, o2 ->
            val p1 = o1.coordinates()
            val p2 = o2.coordinates()
            if (p1.x < p2.x) 1
            else if (p1.x > p2.x) -1
            else if (p1.y < p2.y) 1
            else if (p1.y > p2.y) -1
            else 0
        }
    }

    /**
     * Orders points based on rotation for two ellipses using their focal vectors.
     *
     * This function creates vector pairs from the focal vectors of two ellipses and determines
     * which vector pair contains the points, then returns the points with their corresponding
     * vector pair order. The role (reference point) is automatically calculated as the intersection
     * of the two focal lines.
     *
     * @param ellipse1 The first ellipse
     * @param ellipse2 The second ellipse
     * @param points The points to order
     * @return A list of pairs where each pair contains a point and its vector pair index (0-3)
     */
    fun pointsByRotationFor2Ellipse(
        ellipse1: Ellipse, ellipse2: Ellipse, points: List<Vector3D>
    ): List<Pair<Vector3D, Int>> {
        if (points.isEmpty()) return emptyList()

        // Calculate role as intersection of two focal lines
        val role = calculateRoleFromEllipses(ellipse1, ellipse2)

        // Get focal vectors for both ellipses
        val v1 = getFocalVector(ellipse1)
        val v2 = getFocalVector(ellipse2)

        // Case 1: Both vectors have magnitude > 0
        if (v1.norm() > DEFAULT_TOLERANCE && v2.norm() > DEFAULT_TOLERANCE) {
            return orderPointsWithTwoVectors(v1, v2, role, points)
        }

        // Case 2: Only one vector has magnitude > 0
        if (v1.norm() > DEFAULT_TOLERANCE) {
            val perpendicular = rotateVector90Clockwise(v1)
            return orderPointsWithTwoVectors(v1, perpendicular, role, points)
        }

        if (v2.norm() > DEFAULT_TOLERANCE) {
            val perpendicular = rotateVector90Clockwise(v2)
            return orderPointsWithTwoVectors(v2, perpendicular, role, points)
        }

        // Case 3: Both vectors have magnitude 0, fall back to original pointsByRotation
        // Use a default vector for ordering
        val defaultVector = Vector3D.of(1.0, 0.0, 0.0)
        val orderedPoints = pointsByRotation(defaultVector, role, points)
        return orderedPoints.map { point -> Pair(point, 0) }
    }

    /**
     * Calculates the role (reference point) as the intersection of two focal lines
     */
    private fun calculateRoleFromEllipses(ellipse1: Ellipse, ellipse2: Ellipse): Vector3D {
        // Create focal lines for both ellipses
        val ellipse1FVector = ellipse1.f1.coordinates().vectorTo(ellipse1.f2.coordinates())
        val ellipse2FVector = ellipse2.f1.coordinates().vectorTo(ellipse2.f2.coordinates())

        // Create temporary document for line creation (needed for LineImpl constructor)
        val doc = ellipse1.doc

        val ellipse1FLine = LineImpl(doc, null, ellipse1.f1, ellipse1FVector)
        val ellipse2FLine = LineImpl(doc, null, ellipse2.f1, ellipse2FVector)

        // Calculate intersection of the two focal lines
        val intersection = Intersections.of(ellipse1FLine, ellipse2FLine)

        // If lines don't intersect (parallel), use midpoint between ellipse centers as fallback
        return intersection ?: run {
            val center1 = ellipse1.center
            val center2 = ellipse2.center
            Vector3D.of(
                (center1.x + center2.x) / 2.0, (center1.y + center2.y) / 2.0, (center1.z + center2.z) / 2.0
            )
        }
    }

    /**
     * Gets the focal vector of an ellipse (vector from f1 to f2)
     */
    private fun getFocalVector(ellipse: Ellipse): Vector3D {
        return ellipse.f1.coordinates().vectorTo(ellipse.f2.coordinates())
    }

    /**
     * Rotates a vector 90 degrees clockwise
     */
    private fun rotateVector90Clockwise(vector: Vector3D): Vector3D {
        return Vector3D.of(vector.y, -vector.x, vector.z)
    }

    /**
     * Orders points using two vectors by creating 4 vector pairs and determining
     * which pair contains the points
     */
    private fun orderPointsWithTwoVectors(
        v1: Vector3D, v2: Vector3D, role: Vector3D, points: List<Vector3D>
    ): List<Pair<Vector3D, Int>> {
        // Create 4 vector pairs:
        // 0: v1, v2
        // 1: v1.rotate180, v2
        // 2: v1, v2.rotate180
        // 3: v1.rotate180, v2.rotate180
        val v1Rotated = v1.negate()
        val v2Rotated = v2.negate()

        val vectorPairs = listOf(
            Pair(v1, v2),           // pair 0
            Pair(v1Rotated, v2),    // pair 1
            Pair(v1, v2Rotated),    // pair 2
            Pair(v1Rotated, v2Rotated) // pair 3
        )

        val result = mutableListOf<Pair<Vector3D, Int>>()

        for (point in points) {
            // Find which vector pair this point belongs to
            val pairIndex = findVectorPairForPoint(point, role, vectorPairs)
            result.add(Pair(point, pairIndex))
        }

        // Sort by pair index, then by rotation within each pair
        return result.sortedWith(compareBy<Pair<Vector3D, Int>> { it.second }.thenBy { pair ->
            val vectorPair = vectorPairs[pair.second]
            val combinedVector = vectorPair.first.add(vectorPair.second)
            combinedVector.angleTo(role.vectorTo(pair.first))
        })
    }

    /**
     * Determines which vector pair a point belongs to based on angular relationships
     */
    private fun findVectorPairForPoint(
        point: Vector3D, role: Vector3D, vectorPairs: List<Pair<Vector3D, Vector3D>>
    ): Int {
        val pointVector = role.vectorTo(point)

        for (i in vectorPairs.indices) {
            var (start, end) = vectorPairs[i]

            // Calculate counter-clockwise angle from role-start to role-end
            val startVector = role.vectorTo(role.add(start))
            val endVector = role.vectorTo(role.add(end))
            var angleStartToEnd = calculateCounterClockwiseAngle(startVector, endVector)

            // If angle > 180°, swap start-end and recalculate
            if (angleStartToEnd > PI) {
                val temp = start
                start = end
                end = temp

                val newStartVector = role.vectorTo(role.add(start))
                val newEndVector = role.vectorTo(role.add(end))
                angleStartToEnd = calculateCounterClockwiseAngle(newStartVector, newEndVector)
            }

            // Calculate angle from role-start to role-point
            val newStartVector = role.vectorTo(role.add(start))
            val angleStartToPoint = calculateCounterClockwiseAngle(newStartVector, pointVector)

            // If role-start to role-point angle < role-start to role-end angle, select this pair
            if (angleStartToPoint < angleStartToEnd) {
                return i
            }
        }

        // Fallback: return the first pair if no suitable pair found
        return 0
    }

    /**
     * Calculates the counter-clockwise angle between two vectors
     */
    private fun calculateCounterClockwiseAngle(from: Vector3D, to: Vector3D): Double {
        if (from.norm() < DEFAULT_TOLERANCE || to.norm() < DEFAULT_TOLERANCE) {
            return 0.0
        }

        val fromNorm = from.normalize()
        val toNorm = to.normalize()

        // Calculate angle using atan2 for proper quadrant handling
        val angle = kotlin.math.atan2(
            fromNorm.cross(toNorm).z, // Cross product z-component gives sin of angle
            fromNorm.dot(toNorm)       // Dot product gives cos of angle
        )

        // Ensure angle is in [0, 2π) range
        return if (angle < 0) angle + 2 * PI else angle
    }
}