package viclass.editor.geo.impl.constructor.point

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import org.slf4j.LoggerFactory
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aCircle
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aCircularSector
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLine
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aRay
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aVector
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.anEllipse
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.constructor.Orders.pointsByRotation
import viclass.editor.geo.impl.constructor.Orders.pointsByRotationFor2Ellipse
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.CircleImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
@Singleton
class IntersectionPointEC : ElementConstructor<Point> {
    override val logger = LoggerFactory.getLogger(IntersectionPointEC::class.java)
    private val constraintParam = ConstraintParamDefManager.instance()

    override fun outputType(): KClass<Point> = Point::class

    enum class CGS {
        LineLine, LineCircle, LineSector, LineEllipse, CircleCircle, CircleSector, CircleEllipse, SectorSector, EllipseSector, EllipseEllipse
    }

    override fun template(): ConstructorTemplate = ConstructorTemplateBuilder.create(this).apply {
        cgs(
            ConstraintGroupBuilder.create().name(CGS.LineLine.name).hints("IntersectionOfLineLine")
                .constraint(0, constraintParam[aLine]!!, listOf("NameOfLine"), "tpl-IntersectionOfLine")
                .constraintDepends(
                    1, constraintParam[aLine]!!, listOf(0), listOf("NameOfLine"), "tpl-IntersectionWithLine"
                ).build(),
            ConstraintGroupBuilder.create().name(CGS.LineCircle.name).hints("IntersectionOfLineCircle")
                .constraint(0, constraintParam[aLine]!!, listOf("NameOfLine"), "tpl-IntersectionOfLine")
                .constraintDepends(
                    1, constraintParam[aCircle]!!, listOf(0), listOf("NameOfCircle"), "tpl-IntersectionWithCircle"
                ).constraintOptional(
                    2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection"
                ).build(),
            ConstraintGroupBuilder.create().name(CGS.LineSector.name).hints("IntersectionOfLineSector")
                .constraint(0, constraintParam[aLine]!!, listOf("NameOfLine"), "tpl-IntersectionOfLine")
                .constraintDepends(
                    1,
                    constraintParam[aCircularSector]!!,
                    listOf(0),
                    listOf("NameOfCircularSector"),
                    "tpl-IntersectionWithSector"
                ).constraintOptional(
                    2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection"
                ).build(),
            ConstraintGroupBuilder.create().name(CGS.LineEllipse.name).hints("IntersectionOfLineEllipse")
                .constraint(0, constraintParam[aLine]!!, listOf("NameOfLine"), "tpl-IntersectionOfLine")
                .constraintDepends(
                    1, constraintParam[anEllipse]!!, listOf(0), listOf("NameOfEllipse"), "tpl-IntersectionWithEllipse"
                ).constraintOptional(
                    2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection"
                ).build(),
            ConstraintGroupBuilder.create().name(CGS.CircleCircle.name).hints("IntersectionOfCircleCircle")
                .constraint(0, constraintParam[aCircle]!!, listOf("NameOfCircle"), "tpl-IntersectionOfCircle")
                .constraintDepends(
                    1, constraintParam[aCircle]!!, listOf(0), listOf("NameOfCircle"), "tpl-IntersectionWithCircle"
                ).constraintOptional(
                    2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection"
                ).build(),
            ConstraintGroupBuilder.create().name(CGS.CircleSector.name).hints("IntersectionOfCircleSector")
                .constraint(0, constraintParam[aCircle]!!, listOf("NameOfCircle"), "tpl-IntersectionOfCircle")
                .constraintDepends(
                    1,
                    constraintParam[aCircularSector]!!,
                    listOf(0),
                    listOf("NameOfCircularSector"),
                    "tpl-IntersectionWithSector"
                ).constraintOptional(
                    2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection"
                ).build(),
            ConstraintGroupBuilder.create().name(CGS.CircleEllipse.name).hints("IntersectionOfCircleEllipse")
                .constraint(0, constraintParam[aCircle]!!, listOf("NameOfCircle"), "tpl-IntersectionOfCircle")
                .constraintDepends(
                    1, constraintParam[anEllipse]!!, listOf(0), listOf("NameOfEllipse"), "tpl-IntersectionWithEllipse"
                ).constraintOptional(
                    2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection"
                ).build(),
            ConstraintGroupBuilder.create().name(CGS.SectorSector.name).hints("IntersectionOfSectorSector").constraint(
                0, constraintParam[aCircularSector]!!, listOf("NameOfCircularSector"), "tpl-IntersectionOfSector"
            ).constraintDepends(
                1,
                constraintParam[aCircularSector]!!,
                listOf(0),
                listOf("NameOfCircularSector"),
                "tpl-IntersectionWithSector"
            ).constraintOptional(
                2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection"
            ).build(),
            ConstraintGroupBuilder.create().name(CGS.EllipseSector.name) // Changed name
                .hints("IntersectionOfEllipseSector") // Changed hints
                .constraint(
                    0, // Changed index 0 to Ellipse
                    constraintParam[anEllipse]!!, listOf("NameOfEllipse"), "tpl-IntersectionOfEllipse"
                ).constraintDepends(
                    1, // Changed index 1 to Sector
                    constraintParam[aCircularSector]!!, listOf(0), // Depends on the new index 0 (Ellipse)
                    listOf("NameOfCircularSector"), "tpl-IntersectionWithSector"
                ).constraintOptional(
                    2, // Index 2 remains for Value
                    constraintParam[aValue]!!, listOf(0, 1), // Depends on the new index 0 (Ellipse) and 1 (Sector)
                    listOf("Value"), "tpl-thIntersection"
                ).build(), // This is the block that was SectorEllipse
            ConstraintGroupBuilder.create().name(CGS.EllipseEllipse.name).hints("IntersectionOfEllipseEllipse")
                .constraint(
                    0, constraintParam[anEllipse]!!, listOf("NameOfEllipse"), "tpl-IntersectionOfEllipse"
                ).constraintDepends(
                    1, constraintParam[anEllipse]!!, listOf(0), listOf("NameOfEllipse"), "tpl-IntersectionWithEllipse"
                ).constraintOptional(
                    2, constraintParam[aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thIntersection"
                ).build()
        )
        elTypes(Point::class)
    }.build()

    /**
     * Validates that an intersection point lies on both geometric elements
     * Following the TypeScript approach: convert complex elements to simple forms for intersection,
     * then validate against original elements
     */
    private fun validateIntersectionPoint(point: Vector3D, element1: Element, element2: Element) {
        val isOnElement1 = isPointOnElement(point, element1)
        val isOnElement2 = isPointOnElement(point, element2)

        if (!isOnElement1) throw ConstructionException("Điểm giao không nằm trên phần tử thứ nhất")
        if (!isOnElement2) throw ConstructionException("Điểm giao không nằm trên phần tử thứ hai")
    }

    /**
     * Checks if a point lies on a specific element using appropriate validation methods
     * Similar to TypeScript's isPointOnElement method
     */
    private fun isPointOnElement(point: Vector3D, element: Element): Boolean {
        return when (element) {
            is LineSegment -> Lines.isPointLiesOn(element, point)
            is Ray -> Lines.isPointLiesOn(element, point)
            is VectorVi -> Lines.isPointLiesOn(element, point)
            is CircularSector -> CircularSectors.isOnCircularSector(element, point)
            else -> true // Skip validation for unknown types
        }
    }

    /**
     * Checks if a point lies on an ellipse
     * Uses focal distance property: sum of distances to foci equals 2a
     */
    private fun isPointOnEllipse(ellipse: Ellipse, point: Vector3D): Boolean {
        return try {
            val f1 = ellipse.f1.coordinates()
            val f2 = ellipse.f2.coordinates()
            val dist1 = Distances.of(point, f1)
            val dist2 = Distances.of(point, f2)
            val expectedSum = 2 * ellipse.a
            kotlin.math.abs(dist1 + dist2 - expectedSum) < DEFAULT_TOLERANCE
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Converts a CircularSector to a Circle for intersection calculation
     * Similar to TypeScript's createCircleFromSector method
     */
    private fun createCircleFromSector(sector: CircularSector): Circle {
        return CircleImpl(
            sector.doc, "", sector.centerPoint, sector.radius
        )
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.LineLine -> {
                Validations.validateNumConstraints(c, 2)
                constructLineLine(doc, inputName, c)
            }

            CGS.LineCircle -> {
                Validations.validateNumConstraints(c, 2)
                constructLineCircle(doc, inputName, c)
            }

            CGS.LineSector -> {
                Validations.validateNumConstraints(c, 2)
                constructLineSector(doc, inputName, c)
            }

            CGS.LineEllipse -> {
                Validations.validateNumConstraints(c, 2)
                constructLineEllipse(doc, inputName, c)
            }

            CGS.CircleCircle -> {
                Validations.validateNumConstraints(c, 2)
                constructCircleCircle(doc, inputName, c)
            }

            CGS.CircleSector -> {
                Validations.validateNumConstraints(c, 2)
                constructCircleSector(doc, inputName, c)
            }

            CGS.CircleEllipse -> {
                Validations.validateNumConstraints(c, 2)
                constructCircleEllipse(doc, inputName, c)
            }

            CGS.SectorSector -> {
                Validations.validateNumConstraints(c, 2)
                constructSectorSector(doc, inputName, c)
            }

            CGS.EllipseSector -> {
                Validations.validateNumConstraints(c, 2)
                constructEllipseSector(doc, inputName, c)
            }

            CGS.EllipseEllipse -> {
                Validations.validateNumConstraints(c, 2)
                constructEllipseEllipse(doc, inputName, c)
            }
        }
    }

    private fun constructLineLine(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val lines = mutableListOf<LineVi>()
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEachIndexed { index, param ->
            val extraction = extractFirstPossible<ElementExtraction<LineVi>>(doc, ParamKind.PK_Name, param, c.ctIdx)
            val line = extraction.result.result()
                ?: throw ElementNotExistInDocumentException("không tìm thấy đường thẳng ${index + 1}")
            lines.add(line)
            dependencies.add(extraction.result)
        }

        if (lines.size < 2) throw ConstructionException("Không đủ đường thẳng để tìm giao điểm")

        val intersection =
            Intersections.of(lines[0], lines[1]) ?: throw ConstructionException("Không có điểm giao nhau")

        // Validate that intersection point lies on both lines
        validateIntersectionPoint(intersection, lines[0], lines[1])

        val name = inputName ?: generatePointName(doc)
        val point = PointImpl(doc, name, intersection)

        val result = ConstructionResultImpl<Point>()
        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }

        return result
    }

    private fun intersectionPointLineCircle(
        doc: GeoDoc,
        inputName: String?,
        line: LineVi,
        circle: Circle,
        parallelVector: Vector3D,
        nthIntersection: Int?,
        dependencies: List<ConstructionResult<out Element>>
    ): ConstructionResult<Point> {
        // Calculate all intersections between line and circle
        val allIntersections = Intersections.of(line, circle) ?: throw ConstructionException("Không có điểm giao nhau")

        if (allIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau")
        }

        // Filter intersections that lie on both original elements (line and circle)
        val validIntersections = allIntersections.filter { intersection ->
            isPointOnElement(intersection, line) && isPointOnElement(intersection, circle)
        }

        if (validIntersections.isEmpty()) throw ConstructionException("Không có điểm giao nhau hợp lệ trên cả hai phần tử")

        val actualNthIntersection = nthIntersection ?: 0

        // Validate nth parameter is within bounds of valid intersections
        if (actualNthIntersection < 0 || actualNthIntersection >= validIntersections.size) {
            throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${validIntersections.size} giao điểm hợp lệ.")
        }

        val intersection = validIntersections[actualNthIntersection]

        val name = inputName ?: generatePointName(doc)
        val point = PointImpl(doc, name, intersection.x, intersection.y, intersection.z)

        val result = ConstructionResultImpl<Point>()
        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }
        return result
    }

    private fun intersectionPointLineSector(
        doc: GeoDoc,
        inputName: String?,
        line: LineVi,
        sector: CircularSector,
        parallelVector: Vector3D,
        nthIntersection: Int?,
        dependencies: List<ConstructionResult<out Element>>
    ): ConstructionResult<Point> {
        // Following TypeScript approach: convert sector to circle for intersection calculation
        val sectorAsCircle = createCircleFromSector(sector)

        // Calculate all intersections between line and circle (ignoring sector bounds)
        val allIntersections =
            Intersections.of(line, sectorAsCircle) ?: throw ConstructionException("Không có điểm giao nhau")

        if (allIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau")
        }

        // Filter intersections that lie on both original elements (line and sector)
        val validIntersections = allIntersections.filter { intersection ->
            isPointOnElement(intersection, line) && isPointOnElement(intersection, sector)
        }

        if (validIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau hợp lệ trên cả hai phần tử")
        }

        val actualNthIntersection = nthIntersection ?: 0

        // Validate nth parameter is within bounds of valid intersections
        if (actualNthIntersection < 0 || actualNthIntersection >= validIntersections.size) {
            throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${validIntersections.size} giao điểm hợp lệ.")
        }

        val intersection = validIntersections[actualNthIntersection]

        val name = inputName ?: generatePointName(doc)
        val point = PointImpl(doc, name, intersection.x, intersection.y, intersection.z)

        val result = ConstructionResultImpl<Point>()
        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }
        return result
    }

    private fun intersectionPointCircleSector(
        doc: GeoDoc,
        inputName: String?,
        circle: Circle,
        sector: CircularSector,
        nthIntersection: Int?,
        dependencies: List<ConstructionResult<out Element>>
    ): ConstructionResult<Point> {
        // Following TypeScript approach: convert sector to circle for intersection calculation
        val sectorAsCircle = createCircleFromSector(sector)

        // Calculate all intersections between circles (ignoring sector bounds)
        val allIntersections =
            Intersections.of(circle, sectorAsCircle) ?: throw ConstructionException("Không có điểm giao nhau")

        if (allIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau")
        }

        // Filter intersections that lie on both original elements (circle and sector)
        val validIntersections = allIntersections.filter { intersection ->
            isPointOnElement(intersection, circle) && isPointOnElement(intersection, sector)
        }

        if (validIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau hợp lệ trên cả hai phần tử")
        }

        val actualNthIntersection = nthIntersection ?: 0

        // Validate nth parameter is within bounds of valid intersections
        if (actualNthIntersection < 0 || actualNthIntersection >= validIntersections.size) {
            throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${validIntersections.size} giao điểm hợp lệ.")
        }

        val intersection = validIntersections[actualNthIntersection]

        val name = inputName ?: generatePointName(doc)
        val point = PointImpl(doc, name, intersection.x, intersection.y, intersection.z)

        val result = ConstructionResultImpl<Point>()
        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }
        return result
    }

    private fun intersectionPointLineEllipse(
        doc: GeoDoc,
        inputName: String?,
        line: LineVi,
        ellipse: Ellipse,
        nthIntersection: Int?,
        dependencies: List<ConstructionResult<out Element>>
    ): ConstructionResult<Point> {
        // Calculate all intersections between line and ellipse
        val allIntersections = Intersections.of(line, ellipse) ?: throw ConstructionException("Không có điểm giao nhau")

        if (allIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau")
        }

        // Filter intersections that lie on both original elements (line and ellipse)
        val validIntersections = allIntersections.filter { intersection ->
            isPointOnElement(intersection, line) && isPointOnElement(intersection, ellipse)
        }

        if (validIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau hợp lệ trên cả hai phần tử")
        }

        val actualNthIntersection = nthIntersection ?: 0

        // Validate nth parameter is within bounds of valid intersections
        if (actualNthIntersection < 0 || actualNthIntersection >= validIntersections.size) {
            throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${validIntersections.size} giao điểm hợp lệ.")
        }

        val intersection = validIntersections[actualNthIntersection]

        val name = inputName ?: generatePointName(doc)
        val point = PointImpl(doc, name, intersection.x, intersection.y, intersection.z)

        val result = ConstructionResultImpl<Point>()
        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }
        return result
    }

    private fun intersectionPointEllipseCircle(
        doc: GeoDoc,
        inputName: String?,
        ellipse: Ellipse,
        circle: Circle,
        nthIntersection: Int?,
        dependencies: List<ConstructionResult<out Element>>
    ): ConstructionResult<Point> {
        val intersections = Intersections.of(circle, ellipse) ?: throw ConstructionException("Không có điểm giao nhau")

        if (intersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau")
        }

        val actualNthIntersection = nthIntersection ?: 0

        // Validate nth parameter is within bounds
        if (actualNthIntersection < 0 || actualNthIntersection >= intersections.size) {
            throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${intersections.size} giao điểm.")
        }

        val intersection = intersections[actualNthIntersection]

        val name = inputName ?: generatePointName(doc)
        val point = PointImpl(doc, name, intersection.x, intersection.y, intersection.z)

        val result = ConstructionResultImpl<Point>()
        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }
        return result
    }

    private fun intersectionPointEllipseSector(
        doc: GeoDoc,
        inputName: String?,
        ellipse: Ellipse,
        sector: CircularSector,
        nthIntersection: Int?,
        dependencies: List<ConstructionResult<out Element>>
    ): ConstructionResult<Point> {
        // Following TypeScript approach: convert sector to circle for intersection calculation
        val sectorAsCircle = createCircleFromSector(sector)

        // Calculate all intersections between ellipse and circle (ignoring sector bounds)
        val allIntersections =
            Intersections.of(ellipse, sectorAsCircle) ?: throw ConstructionException("Không có điểm giao nhau")

        if (allIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau")
        }

        // Filter intersections that lie on both original elements (ellipse and sector)
        val validIntersections = allIntersections.filter { intersection ->
            isPointOnElement(intersection, ellipse) && isPointOnElement(intersection, sector)
        }

        if (validIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau hợp lệ trên cả hai phần tử")
        }

        val actualNthIntersection = nthIntersection ?: 0

        // Validate nth parameter is within bounds of valid intersections
        if (actualNthIntersection < 0 || actualNthIntersection >= validIntersections.size) {
            throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${validIntersections.size} giao điểm hợp lệ.")
        }

        val intersection = validIntersections[actualNthIntersection]

        val name = inputName ?: generatePointName(doc)
        val point = PointImpl(doc, name, intersection.x, intersection.y, intersection.z)

        val result = ConstructionResultImpl<Point>()
        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }
        return result
    }

    private fun constructLineCircle(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        var tempCircle: Circle? = null
        var tempLine: LineVi? = null
        var tempNthIntersection: Int? = null
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEach { param ->
            when (param.paramDef.id) {
                aLine, aLineSegment, aVector, aRay -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineVi>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    val extractedLine = extraction.result.result()
                    if (extractedLine != null) {
                        tempLine = extractedLine
                        dependencies.add(extraction.result)
                    } else {
                        throw ElementNotExistInDocumentException("không tìm thấy đường thẳng")
                    }
                }

                aCircle -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<Circle>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    val extractedCircle = extraction.result.result()
                    if (extractedCircle != null) {
                        tempCircle = extractedCircle
                        dependencies.add(extraction.result)
                    } else {
                        throw ElementNotExistInDocumentException("không tìm thấy đường tròn")
                    }
                }

                aValue -> if (param.specs.indexInCG == 2) {
                    val extraction =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, param, c.ctIdx)
                    tempNthIntersection = extraction.result - 1
                }
            }
        }

        val line = tempLine ?: throw ElementNotExistInDocumentException(
            "Không tìm thấy đường thẳng"
        )
        val circle = tempCircle ?: throw ElementNotExistInDocumentException(
            "Không tìm thấy đường tròn"
        )
        val parallelVector = line.parallelVector

        return intersectionPointLineCircle(
            doc, inputName, line, circle, parallelVector, tempNthIntersection, dependencies
        )
    }

    private fun constructLineSector(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        var tempSector: CircularSector? = null
        var tempLine: LineVi? = null
        var tempNthIntersection: Int? = null
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEach { param ->
            when (param.paramDef.id) {
                aLine, aLineSegment, aVector, aRay -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineVi>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    val extractedLine = extraction.result.result()
                    if (extractedLine != null) {
                        tempLine = extractedLine
                        dependencies.add(extraction.result)
                    } else {
                        throw ElementNotExistInDocumentException("không tìm thấy đường thẳng")
                    }
                }

                aCircularSector -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<CircularSector>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    val extractedSector = extraction.result.result()
                    if (extractedSector != null) {
                        tempSector = extractedSector
                        dependencies.add(extraction.result)
                    } else {
                        throw ElementNotExistInDocumentException("không tìm thấy hình quạt tròn")
                    }
                }

                aValue -> if (param.specs.indexInCG == 2) {
                    val extraction =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, param, c.ctIdx)
                    tempNthIntersection = extraction.result - 1
                }
            }
        }

        val line = tempLine ?: throw ElementNotExistInDocumentException(
            "Tham số đường thẳng không được cung cấp hoặc không tìm thấy"
        )
        val sector = tempSector ?: throw ElementNotExistInDocumentException(
            "Tham số hình quạt tròn không được cung cấp hoặc không tìm thấy"
        )
        val parallelVector = line.parallelVector

        return intersectionPointLineSector(
            doc, inputName, line, sector, parallelVector, tempNthIntersection, dependencies
        )
    }

    private fun constructCircleSector(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        var circle: Circle? = null
        var sector: CircularSector? = null
        var nthIntersection: Int? = null
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aCircle -> {
                    val extraction = extractFirstPossible<ElementExtraction<Circle>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    val extractedResult = extraction.result
                    circle = extractedResult.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy đường tròn")
                    dependencies.add(extractedResult)
                }

                aCircularSector -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<CircularSector>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    val extractedResult = extraction.result
                    sector = extractedResult.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy hình quạt tròn")
                    dependencies.add(extractedResult)
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    nthIntersection = extractedResult.result - 1
                }
            }
        }
        val cir = circle ?: throw ElementNotExistInDocumentException(
            "Tham số đường tròn không được cung cấp hoặc không tìm thấy"
        )
        val s = sector ?: throw ElementNotExistInDocumentException(
            "Tham số hình quạt tròn không được cung cấp hoặc không tìm thấy"
        )

        return intersectionPointCircleSector(doc, inputName, cir, s, nthIntersection, dependencies)
    }

    private fun constructLineEllipse(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        var ellipse: Ellipse? = null
        var line: LineVi? = null
        var nthIntersection: Int? = null
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEach {
            when (it.paramDef.id) {
                aLine, aLineSegment, aVector, aRay -> {
                    val extractedLineResult =
                        extractFirstPossible<ElementExtraction<LineVi>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    line = extractedLineResult.result.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy đường thẳng")
                    dependencies.add(extractedLineResult.result)
                }

                anEllipse -> {
                    val extractedEllipseResult =
                        extractFirstPossible<ElementExtraction<Ellipse>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    ellipse = extractedEllipseResult.result.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy hình elip")
                    dependencies.add(extractedEllipseResult.result)
                }

                aValue -> if (it.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, it, c.ctIdx)
                    nthIntersection = extractedResult.result - 1
                }
            }
        }
        val l = line ?: throw ElementNotExistInDocumentException(
            "Tham số đường thẳng không được cung cấp hoặc không tìm thấy"
        )
        val e = ellipse
            ?: throw ElementNotExistInDocumentException("Tham số hình elip không được cung cấp hoặc không tìm thấy")

        return intersectionPointLineEllipse(doc, inputName, l, e, nthIntersection, dependencies)
    }

    private fun constructCircleEllipse(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        var ellipse: Ellipse? = null
        var circle: Circle? = null
        var nthIntersection: Int? = null
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEach {
            when (it.paramDef.id) {
                aCircle -> {
                    val extractedCircleResult =
                        extractFirstPossible<ElementExtraction<Circle>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    circle = extractedCircleResult.result.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy đường tròn")
                    dependencies.add(extractedCircleResult.result)
                }

                anEllipse -> {
                    val extractedEllipseResult =
                        extractFirstPossible<ElementExtraction<Ellipse>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    ellipse = extractedEllipseResult.result.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy hình elip")
                    dependencies.add(extractedEllipseResult.result)
                }

                aValue -> if (it.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, it, c.ctIdx)
                    nthIntersection = extractedResult.result - 1
                }
            }
        }
        val cir = circle ?: throw ElementNotExistInDocumentException(
            "Tham số đường tròn không được cung cấp hoặc không tìm thấy"
        )
        val e = ellipse
            ?: throw ElementNotExistInDocumentException("Tham số hình elip không được cung cấp hoặc không tìm thấy")

        return intersectionPointEllipseCircle(doc, inputName, e, cir, nthIntersection, dependencies)
    }

    private fun constructEllipseSector(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        var sector: CircularSector? = null
        var ellipse: Ellipse? = null
        var nthIntersection: Int? = null
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEach {
            when (it.paramDef.id) {
                aCircularSector -> {
                    val extractedSectorResult =
                        extractFirstPossible<ElementExtraction<CircularSector>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    sector = extractedSectorResult.result.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy hình quạt tròn")
                    dependencies.add(extractedSectorResult.result)
                }

                anEllipse -> {
                    val extractedEllipseResult =
                        extractFirstPossible<ElementExtraction<Ellipse>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    ellipse = extractedEllipseResult.result.result()
                        ?: throw ElementNotExistInDocumentException("không tìm thấy hình elip")
                    dependencies.add(extractedEllipseResult.result)
                }

                aValue -> if (it.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, it, c.ctIdx)
                    nthIntersection = extractedResult.result - 1
                }
            }
        }
        val s = sector ?: throw ElementNotExistInDocumentException(
            "Tham số hình quạt tròn không được cung cấp hoặc không tìm thấy"
        )
        val e = ellipse
            ?: throw ElementNotExistInDocumentException("Tham số hình elip không được cung cấp hoặc không tìm thấy")

        return intersectionPointEllipseSector(doc, inputName, e, s, nthIntersection, dependencies)
    }

    private fun constructCircleCircle(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val dependencies = mutableListOf<ConstructionResult<out Element>>()
        var circle1: Circle? = null
        var circle2: Circle? = null
        var tempNthIntersection: Int? = null

        c.params.forEachIndexed { index, p ->
            when (p.paramDef.id) {
                aCircle -> {
                    val extraction = extractFirstPossible<ElementExtraction<Circle>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    val extractedResult = extraction.result
                    val extractedCircle = extractedResult.result()
                    if (index == 0) circle1 = extractedCircle else circle2 = extractedCircle
                    if (extractedCircle == null) throw ElementNotExistInDocumentException("không tìm thấy đường tròn ${index + 1}")
                    dependencies.add(extractedResult)
                }

                aValue -> if (p.specs.indexInCG == 2) { // Note: Template has index 1, but code uses index 2? Sticking to 2 for now.
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    val th = extractedResult.result
                    tempNthIntersection = th - 1
                }
            }
        }

        val c1 = circle1 ?: throw ElementNotExistInDocumentException("Không tìm thấy các đường tròn")
        val c2 = circle2 ?: throw ElementNotExistInDocumentException("Không tìm thấy các đường tròn")
        val nthIntersection = tempNthIntersection // Make it a val

        val center1 = c1.centerPoint.coordinates()
        val center2 = c2.centerPoint.coordinates()

        val intersections = Intersections.of(c1, c2) ?: throw ConstructionException("Không có điểm giao nhau")
        val orderedIntersections = pointsByRotation(center1.vectorTo(center2), center1, intersections)

        if (orderedIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau")
        }

        val actualNthIntersection = nthIntersection ?: 0

        // Validate nth parameter is within bounds
        if (actualNthIntersection < 0 || actualNthIntersection >= orderedIntersections.size) {
            throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${orderedIntersections.size} giao điểm.")
        }

        val intersection = orderedIntersections[actualNthIntersection]

        val name = inputName ?: generatePointName(doc)

        val point = PointImpl(doc, name, intersection.x, intersection.y, intersection.z)

        val result = ConstructionResultImpl<Point>()
        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }

        return result
    }

    private fun constructSectorSector(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val result = ConstructionResultImpl<Point>()

        var tempNthIntersection: Int? = null
        var sector1: CircularSector? = null
        var sector2: CircularSector? = null
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEachIndexed { index, p ->
            when (p.paramDef.id) {
                aCircularSector -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<CircularSector>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    val extractedResult = extraction.result
                    if (index == 0) sector1 = extractedResult.result() else sector2 = extractedResult.result()
                    if (extractedResult.result() == null) throw ElementNotExistInDocumentException("không tìm thấy hình quạt tròn ${index + 1}")
                    dependencies.add(extractedResult)
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extraction = extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    tempNthIntersection = extraction.result - 1
                }
            }
        }

        val s1 = sector1 ?: throw ElementNotExistInDocumentException("Không tìm thấy các hình quạt tròn")
        val s2 = sector2 ?: throw ElementNotExistInDocumentException("Không tìm thấy các hình quạt tròn")
        val nthIntersection = tempNthIntersection

        // Following TypeScript approach: convert sectors to circles for intersection calculation
        val s1AsCircle = createCircleFromSector(s1)
        val s2AsCircle = createCircleFromSector(s2)

        // Calculate all intersections between circles (ignoring sector bounds)
        val allIntersections =
            Intersections.of(s1AsCircle, s2AsCircle) ?: throw ConstructionException("Không có điểm giao nhau")

        if (allIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau")
        }

        // Filter intersections that lie on both original sectors
        val validIntersections = allIntersections.filter { intersection ->
            isPointOnElement(intersection, s1) && isPointOnElement(intersection, s2)
        }

        if (validIntersections.isEmpty()) {
            throw ConstructionException("Không có điểm giao nhau hợp lệ trên cả hai hình quạt tròn")
        }

        val actualNthIntersection = nthIntersection ?: 0

        // Validate nth parameter is within bounds of valid intersections
        if (actualNthIntersection < 0 || actualNthIntersection >= validIntersections.size) {
            throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${validIntersections.size} giao điểm hợp lệ.")
        }

        val intersection = validIntersections[actualNthIntersection]

        val name = inputName ?: generatePointName(doc)
        val point = PointImpl(doc, name, intersection.x, intersection.y, intersection.z)

        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }

        return result
    }

    private fun constructEllipseEllipse(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val result = ConstructionResultImpl<Point>()
        val ellipses = mutableListOf<Ellipse>()
        var tempNthIntersection: Int? = null
        val dependencies = mutableListOf<ConstructionResult<out Element>>()

        c.params.forEachIndexed { index, p ->
            when (p.paramDef.id) {
                anEllipse -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<Ellipse>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    val extractedResult = extraction.result
                    val extractedEllipse = extractedResult.result()
                    ellipses.add(
                        extractedEllipse
                            ?: throw ElementNotExistInDocumentException("không tìm thấy hình elip ${index + 1}")
                    )
                    dependencies.add(extractedResult)
                }

                aValue -> if (p.specs.indexInCG == 2) { // Note: Template has index 1, but code uses index 2? Sticking to 2 for now.
                    // Check template CGS.EllipseEllipse.
                    val extraction = extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    tempNthIntersection = extraction.result - 1
                }
            }
        }

        if (ellipses.size < 2) {
            throw ElementNotExistInDocumentException("Không đủ hình elip để tìm giao điểm")
        }
        val nthIntersection = tempNthIntersection // Make it a val
        val ellipse1 = ellipses[0]
        val ellipse2 = ellipses[1]

        val intersections =
            Intersections.of(ellipse1, ellipse2) ?: throw ConstructionException("Không có điểm giao nhau")

        // Use the updated pointsByRotationFor2Ellipse function which automatically calculates role
        val orderedIntersectionsWithPairIndex = pointsByRotationFor2Ellipse(ellipse1, ellipse2, intersections)

        if (orderedIntersectionsWithPairIndex.isEmpty())
            throw ConstructionException("Không có điểm giao nhau")

        logger.info("Ordered intersections with pair index: $orderedIntersectionsWithPairIndex")

        val actualNthIntersection = nthIntersection ?: 0

        // Get the intersection point from the pair (point, pairIndex)
        val intersectionPair = orderedIntersectionsWithPairIndex.find({ it.second == actualNthIntersection })
            ?: throw ConstructionException("Chỉ số giao điểm ${actualNthIntersection} không hợp lệ.")
        val intersection = intersectionPair.first // Get the Vector3D from the pair

        val name = inputName ?: generatePointName(doc)

        val point = PointImpl(doc, name, intersection.x, intersection.y, intersection.z)

        result.setResult(point)
        dependencies.forEach { result.mergeAsDependency(it) }

        return result
    }
}
