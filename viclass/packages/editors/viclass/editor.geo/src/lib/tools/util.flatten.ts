import { Circle, Line, point, Ray, Segment, vector } from '@flatten-js/core';
import { RenderCircle, RenderLine, RenderLineSegment, RenderRay } from '../model';
import { GeoDocCtrl } from '../objects';
/**
 * Create Flatten.js Line, Ray, or Segment from RenderLine, RenderRay, or RenderLineSegment using Flatten.js geometry
 */
export function createFlattenLine(renderObj: RenderLine | RenderRay | RenderLineSegment, docCtrl: GeoDocCtrl): Line {
    const startCoords = renderObj.coord('start', docCtrl.rendererCtrl);
    const startPoint = point(startCoords[0], startCoords[1]);

    const orderedVector = renderObj.orderedVector(docCtrl.rendererCtrl);
    const v = vector(orderedVector[0], orderedVector[1]);

    return new Line(startPoint, v.rotate90CW());
}

export function createExtractFlattenLine(
    renderObj: RenderLine | RenderRay | RenderLineSegment,
    docCtrl: GeoDocCtrl
): Line | Segment | Ray {
    const startCoords = renderObj.coord('start', docCtrl.rendererCtrl);
    const startPoint = point(startCoords[0], startCoords[1]);

    const orderedVector = renderObj.orderedVector(docCtrl.rendererCtrl);
    const v = vector(orderedVector[0], orderedVector[1]);

    if (renderObj.type === 'RenderRay') {
        // For Ray, we need normal vector (perpendicular to direction)
        // Try using rotate90CW instead of rotate90CCW
        return new Ray(startPoint, v.rotate90CW());
    } else if (renderObj.type === 'RenderLineSegment' || renderObj.type === 'RenderVector') {
        const endCoords = renderObj.coord('end', docCtrl.rendererCtrl);
        return new Segment(startPoint, point(endCoords[0], endCoords[1]));
    }

    // For infinite line, we also need normal vector
    return new Line(startPoint, v.rotate90CW());
}

/**
 * Create Flatten.js Circle from RenderCircle using Flatten.js geometry
 */
export function createFlattenCircle(renderCircle: RenderCircle, docCtrl: GeoDocCtrl): Circle {
    // Get center coordinates using proper coord method
    const centerCoords = renderCircle.coord('center', docCtrl.rendererCtrl);
    const radius = renderCircle.radius;
    const center = point(centerCoords[0], centerCoords[1]);
    return new Circle(center, radius);
}
