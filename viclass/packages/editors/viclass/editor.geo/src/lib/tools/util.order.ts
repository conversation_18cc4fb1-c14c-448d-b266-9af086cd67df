import { Line, Point, point, Circle } from '@flatten-js/core';

/**
 * Ordering utilities for intersection results
 * Based on the Kotlin Orders utility class from intersection.utils.kt and order.result.utils.kt
 *
 * These functions provide consistent ordering of intersection points to ensure deterministic results
 * across different geometric operations. The ordering strategies include:
 * - Points along parallel vectors (for line intersections)
 * - Points on circles by angle
 * - Points on ellipses by angle relative to focal axis
 * - Points by rotation around reference vectors
 * - Document coordinate ordering (left-to-right, top-to-bottom)
 */

const DEFAULT_TOLERANCE = Number.EPSILON;

/**
 * Determines the direction of a point relative to a root point along a given parallel vector.
 * @param parallel The parallel vector defining the direction
 * @param root The root point
 * @param p The point to determine the direction for
 * @returns -1 if the point is in the direction of the vector, 1 otherwise
 */
export function directionOfPointOnParallelVector(parallel: Point, root: Point, p: Point): number {
    const vecUnit = normalizeVector(parallel);
    const k1 = dotProduct(root, vecUnit);
    const k2 = dotProduct(p, vecUnit);
    return k1 < k2 ? -1 : 1;
}

/**
 * Sorts points on a line based on a parallel vector.
 * Assumes all points lie on the line defined by the parallel vector.
 * @param parallel The vector parallel to the line
 * @param points Array of points to sort
 * @returns Array of points sorted along the line
 */
export function pointsOnParallelVector(parallel: Point, points: Point[]): Point[] {
    if (points.length < 2) return points;

    const vecUnit = normalizeVector(parallel);
    return points.sort((a, b) => {
        const dotA = dotProduct(a, vecUnit);
        const dotB = dotProduct(b, vecUnit);
        return dotA - dotB;
    });
}

/**
 * Sorts points on a circle based on their angle relative to a reference point.
 * Assumes all points lie on the circle.
 *
 * This implementation follows the Kotlin logic more closely by calculating
 * the reference angle more precisely and using proper angle normalization.
 *
 * @param circle The circle on which the points lie
 * @param refPoint A point on the circle used as reference for angle calculation
 * @param points Array of points to sort
 * @returns Array of points sorted by their angle on the circle
 */
export function pointsOnCircle(circle: Circle, refPoint: Point, points: Point[]): Point[] {
    if (points.length < 2) return points;

    const center = circle.center;

    // Calculate reference angle more precisely
    // In Kotlin, this involves intersection calculations, but we simplify here
    // while maintaining the same angle normalization logic
    const angleRef = angleOfPoint(center, refPoint);

    return points.sort((a, b) => {
        let angleA = angleOfPoint(center, a);
        let angleB = angleOfPoint(center, b);

        // Normalize angles relative to reference (following Kotlin logic exactly)
        if (angleA >= angleRef) {
            angleA = angleA - angleRef;
        } else {
            angleA = 2 * Math.PI - angleRef + angleA;
        }

        if (angleB >= angleRef) {
            angleB = angleB - angleRef;
        } else {
            angleB = 2 * Math.PI - angleRef + angleB;
        }

        return angleA - angleB;
    });
}

/**
 * Sorts points on an ellipse based on their angle relative to the vector from center to f2.
 * Assumes all points lie on the ellipse.
 * @param ellipse The ellipse geometry with f2 as second focus point
 * @param points Array of points to sort
 * @returns Array of points sorted by their angle on the ellipse
 */
export function pointsOnEllipse(ellipse: { center: Point; f2: Point }, points: Point[]): Point[] {
    if (points.length < 2) return points;

    const center = ellipse.center;
    const f2 = ellipse.f2;
    const vecCF2 = vectorFromTo(center, f2);

    return points.sort((a, b) => {
        const vecA = vectorFromTo(center, a);
        const vecB = vectorFromTo(center, b);
        const angleA = angleBetweenVectors(vecCF2, vecA);
        const angleB = angleBetweenVectors(vecCF2, vecB);
        return angleA - angleB;
    });
}

/**
 * Sorts points based on their angle relative to a reference vector and a role vector.
 * @param vec The reference vector
 * @param role The role vector
 * @param points Array of points to sort
 * @returns Array of points sorted by their angle
 */
export function pointsByRotation(vec: Point, role: Point, points: Point[]): Point[] {
    return points.sort((a, b) => {
        const vecToA = vectorFromTo(role, a);
        const vecToB = vectorFromTo(role, b);
        const angleA = signedAngleBetweenVectors(vec, vecToA);
        const angleB = signedAngleBetweenVectors(vec, vecToB);
        return angleA - angleB;
    });
}

/**
 * Orders two points based on their angle relative to a parallel vector and role vector.
 * @param parallel The parallel vector defining the main direction
 * @param role The role vector providing reference orientation
 * @param v1 The first point
 * @param v2 The second point
 * @returns Array containing v1 and v2 in determined order, or empty array if ambiguous
 */
export function pointByParallelVector(parallel: Point, role: Point, v1: Point, v2: Point): Point[] {
    if (pointsEqual(v1, v2)) return [v1, v2];

    const vecTo1 = vectorFromTo(role, v1);
    const vecTo2 = vectorFromTo(role, v2);
    const angle1 = signedAngleBetweenVectors(parallel, vecTo1);
    const angle2 = signedAngleBetweenVectors(parallel, vecTo2);

    if (angle1 < Math.PI && angle2 > Math.PI) return [v1, v2];
    if (angle2 < Math.PI && angle1 > Math.PI) return [v2, v1];
    return [];
}

/**
 * Orders two points based on their counter-clockwise angle relative to a reference line.
 * @param refLine The reference line
 * @param v1 The first point
 * @param v2 The second point
 * @returns Array containing v1 and v2 in counter-clockwise order, or empty array if ambiguous
 */
export function pointsBaseLineReference(refLine: Line, v1: Point, v2: Point): Point[] {
    if (pointsEqual(v1, v2)) return [v1, v2];

    // Get direction vector from line
    const lineStart = refLine.start;
    const lineEnd = refLine.end;
    const refVector = vectorFromTo(lineStart, lineEnd);

    const vec1 = vectorFromTo(lineStart, v1);
    const vec2 = vectorFromTo(lineStart, v2);

    const angle1 = signedAngleBetweenVectors(refVector, vec1);
    const angle2 = signedAngleBetweenVectors(refVector, vec2);

    if (angle1 < angle2) return [v1, v2];
    else if (angle1 > angle2) return [v2, v1];
    else return []; // Collinear or ambiguous
}

/**
 * Orders points in document coordinate system from left to right, then top to bottom.
 *
 * Following the exact Kotlin logic:
 * - Primary sort by x-coordinate (leftmost first gets higher priority = 1)
 * - Secondary sort by y-coordinate (topmost first gets higher priority = 1)
 *
 * @param points Array of points to sort
 * @returns Array of points sorted by document coordinates
 */
export function pointsInDocument(points: Point[]): Point[] {
    return points.sort((o1, o2) => {
        // Exact translation of Kotlin comparator logic
        if (o1.x < o2.x)
            return 1; // o1 is more left -> higher priority
        else if (o1.x > o2.x)
            return -1; // o1 is more right -> lower priority
        else if (o1.y < o2.y)
            return 1; // same x, o1 is higher -> higher priority
        else if (o1.y > o2.y)
            return -1; // same x, o1 is lower -> lower priority
        else return 0; // same position
    });
}

/**
 * Filters intersection points that lie within a sector's angular range
 * and orders them appropriately. Based on Kotlin sector intersection logic.
 * @param intersections Array of potential intersection points
 * @param sector The sector to filter against
 * @returns Filtered and ordered intersection points within the sector
 */
export function filterAndOrderSectorIntersections(
    intersections: Point[],
    sector: { center: Point; radius: number; startAngle: number; endAngle: number }
): Point[] {
    const filteredPoints: Point[] = [];

    for (const pt of intersections) {
        const distance = Math.sqrt((pt.x - sector.center.x) ** 2 + (pt.y - sector.center.y) ** 2);

        // Check if point is on the circle (within tolerance)
        if (Math.abs(distance - sector.radius) < DEFAULT_TOLERANCE) {
            // Calculate angle from center to point
            const dx = pt.x - sector.center.x;
            const dy = pt.y - sector.center.y;
            let pointAngle = Math.atan2(dy, dx);
            if (pointAngle < 0) pointAngle += 2 * Math.PI;

            // Check if point is within sector's angular range
            let startAngle = sector.startAngle;
            let endAngle = sector.endAngle;

            // Normalize angles
            if (startAngle < 0) startAngle += 2 * Math.PI;
            if (endAngle < 0) endAngle += 2 * Math.PI;

            // Handle angle wrapping
            const isInSector =
                startAngle <= endAngle
                    ? pointAngle >= startAngle && pointAngle <= endAngle
                    : pointAngle >= startAngle || pointAngle <= endAngle;

            if (isInSector) {
                filteredPoints.push(pt);
            }
        }
    }

    // Order by angle if multiple points
    if (filteredPoints.length > 1) {
        return filteredPoints.sort((a, b) => {
            const angleA = Math.atan2(a.y - sector.center.y, a.x - sector.center.x);
            const angleB = Math.atan2(b.y - sector.center.y, b.x - sector.center.x);
            return angleA - angleB;
        });
    }

    return filteredPoints;
}

/**
 * Orders points based on their creation order within a document context.
 *
 * Since TypeScript doesn't have direct access to GeoDoc like Kotlin,
 * this function takes an array of points with creation indices.
 *
 * @param pointsWithIndices Array of objects containing point and creation index
 * @returns Array of points sorted by their creation order
 */
export function pointByCreateTime(pointsWithIndices: Array<{ point: Point; index: number }>): Point[] {
    return pointsWithIndices.sort((a, b) => a.index - b.index).map(item => item.point);
}

/**
 * Creates an ellipse object with focal points from center, semi-axes, and rotation
 * @param center Center point of the ellipse
 * @param a Semi-major axis length
 * @param b Semi-minor axis length
 * @param rotate Rotation angle in radians
 * @returns Ellipse object with center, f1, and f2 properties
 */
export function createEllipseWithFoci(
    center: Point,
    a: number,
    b: number,
    rotate: number
): { center: Point; f1: Point; f2: Point } {
    // Calculate focal distance c = sqrt(a^2 - b^2) for a > b, or sqrt(b^2 - a^2) for b > a
    const isAMajor = a >= b;
    const majorAxis = isAMajor ? a : b;
    const minorAxis = isAMajor ? b : a;
    const c = Math.sqrt(majorAxis * majorAxis - minorAxis * minorAxis);

    // Adjust rotation angle if b is the major axis
    const effectiveRotate = isAMajor ? rotate : rotate + Math.PI / 2;

    // Calculate focal points along the major axis
    const cosRotate = Math.cos(effectiveRotate);
    const sinRotate = Math.sin(effectiveRotate);

    const f1 = point(center.x + c * cosRotate, center.y + c * sinRotate);

    const f2 = point(center.x - c * cosRotate, center.y - c * sinRotate);

    return { center, f1, f2 };
}

/**
 * Orders points based on rotation for two ellipses using their focal vectors.
 *
 * This function creates vector pairs from the focal vectors of two ellipses and determines
 * which vector pair contains the points, then returns the points with their corresponding
 * vector pair order. The role (reference point) is automatically calculated as the intersection
 * of the two focal lines.
 *
 * @param ellipse1 The first ellipse with center, f1, and f2 properties
 * @param ellipse2 The second ellipse with center, f1, and f2 properties
 * @param points The points to order
 * @returns Array of pairs where each pair contains a point and its vector pair index (0-3)
 */
export function pointsByRotationFor2Ellipse(
    ellipse1: { center: Point; f1: Point; f2: Point },
    ellipse2: { center: Point; f1: Point; f2: Point },
    points: Point[]
): Array<{ point: Point; pairIndex: number }> {
    if (points.length === 0) return [];

    // Calculate role as intersection of two focal lines
    const role = calculateRoleFromEllipses(ellipse1, ellipse2);

    // Get focal vectors for both ellipses
    const v1 = getFocalVector(ellipse1);
    const v2 = getFocalVector(ellipse2);

    // Case 1: Both vectors have magnitude > 0
    if (vectorMagnitude(v1) > DEFAULT_TOLERANCE && vectorMagnitude(v2) > DEFAULT_TOLERANCE) {
        return orderPointsWithTwoVectors(v1, v2, role, points);
    }

    // Case 2: Only one vector has magnitude > 0
    if (vectorMagnitude(v1) > DEFAULT_TOLERANCE) {
        const perpendicular = rotateVector90Clockwise(v1);
        return orderPointsWithTwoVectors(v1, perpendicular, role, points);
    }

    if (vectorMagnitude(v2) > DEFAULT_TOLERANCE) {
        const perpendicular = rotateVector90Clockwise(v2);
        return orderPointsWithTwoVectors(v2, perpendicular, role, points);
    }

    // Case 3: Both vectors have magnitude 0, fall back to original pointsByRotation
    // Use a default vector for ordering
    const defaultVector = point(1.0, 0.0);
    const orderedPoints = pointsByRotation(defaultVector, role, points);
    return orderedPoints.map(pt => ({ point: pt, pairIndex: 0 }));
}

// Helper functions for vector operations

function normalizeVector(v: Point): Point {
    const magnitude = Math.sqrt(v.x * v.x + v.y * v.y);
    if (magnitude < DEFAULT_TOLERANCE) return point(0, 0);
    return point(v.x / magnitude, v.y / magnitude);
}

function dotProduct(a: Point, b: Point): number {
    return a.x * b.x + a.y * b.y;
}

function vectorFromTo(from: Point, to: Point): Point {
    return point(to.x - from.x, to.y - from.y);
}

function angleOfPoint(center: Point, p: Point): number {
    const dx = p.x - center.x;
    const dy = p.y - center.y;
    let angle = Math.atan2(dy, dx);
    if (angle < 0) angle += 2 * Math.PI;
    return angle;
}

function angleBetweenVectors(v1: Point, v2: Point): number {
    const dot = dotProduct(v1, v2);
    const mag1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
    const mag2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);

    if (mag1 < DEFAULT_TOLERANCE || mag2 < DEFAULT_TOLERANCE) return 0;

    const cosAngle = Math.max(-1, Math.min(1, dot / (mag1 * mag2)));
    return Math.acos(cosAngle);
}

function signedAngleBetweenVectors(v1: Point, v2: Point): number {
    const angle = Math.atan2(v2.y, v2.x) - Math.atan2(v1.y, v1.x);
    return angle < 0 ? angle + 2 * Math.PI : angle;
}

function pointsEqual(p1: Point, p2: Point): boolean {
    return Math.abs(p1.x - p2.x) < DEFAULT_TOLERANCE && Math.abs(p1.y - p2.y) < DEFAULT_TOLERANCE;
}

// Helper functions for pointsByRotationFor2Ellipse

/**
 * Calculates the role (reference point) as the intersection of two focal lines
 */
function calculateRoleFromEllipses(
    ellipse1: { center: Point; f1: Point; f2: Point },
    ellipse2: { center: Point; f1: Point; f2: Point }
): Point {
    // Create focal vectors for both ellipses
    const ellipse1FVector = vectorFromTo(ellipse1.f1, ellipse1.f2);
    const ellipse2FVector = vectorFromTo(ellipse2.f1, ellipse2.f2);

    // Calculate intersection of the two focal lines
    const intersection = calculateLineIntersection(ellipse1.f1, ellipse1FVector, ellipse2.f1, ellipse2FVector);

    // If lines don't intersect (parallel), use midpoint between ellipse centers as fallback
    if (intersection === null) {
        const center1 = ellipse1.center;
        const center2 = ellipse2.center;
        return point((center1.x + center2.x) / 2.0, (center1.y + center2.y) / 2.0);
    }

    return intersection;
}

/**
 * Gets the focal vector of an ellipse (vector from f1 to f2)
 */
function getFocalVector(ellipse: { f1: Point; f2: Point }): Point {
    return vectorFromTo(ellipse.f1, ellipse.f2);
}

/**
 * Calculates the magnitude of a vector
 */
function vectorMagnitude(vector: Point): number {
    return Math.sqrt(vector.x * vector.x + vector.y * vector.y);
}

/**
 * Rotates a vector 90 degrees clockwise
 */
function rotateVector90Clockwise(vector: Point): Point {
    return point(vector.y, -vector.x);
}

/**
 * Orders points using two vectors by creating 4 vector pairs and determining
 * which pair contains the points
 */
function orderPointsWithTwoVectors(
    v1: Point,
    v2: Point,
    role: Point,
    points: Point[]
): Array<{ point: Point; pairIndex: number }> {
    // Create 4 vector pairs:
    // 0: v1, v2
    // 1: v1.rotate180, v2
    // 2: v1, v2.rotate180
    // 3: v1.rotate180, v2.rotate180
    const v1Rotated = point(-v1.x, -v1.y);
    const v2Rotated = point(-v2.x, -v2.y);

    const vectorPairs = [
        { first: v1, second: v2 }, // pair 0
        { first: v1Rotated, second: v2 }, // pair 1
        { first: v1, second: v2Rotated }, // pair 2
        { first: v1Rotated, second: v2Rotated }, // pair 3
    ];

    const result: Array<{ point: Point; pairIndex: number }> = [];

    for (const pt of points) {
        // Find which vector pair this point belongs to
        const pairIndex = findVectorPairForPoint(pt, role, vectorPairs);
        result.push({ point: pt, pairIndex });
    }

    // Sort by pair index, then by rotation within each pair
    return result.sort((a, b) => {
        if (a.pairIndex !== b.pairIndex) {
            return a.pairIndex - b.pairIndex;
        }

        // Sort within the same pair by rotation angle
        const vectorPair = vectorPairs[a.pairIndex];
        const combinedVector = point(
            vectorPair.first.x + vectorPair.second.x,
            vectorPair.first.y + vectorPair.second.y
        );

        const vecToA = vectorFromTo(role, a.point);
        const vecToB = vectorFromTo(role, b.point);
        const angleA = signedAngleBetweenVectors(combinedVector, vecToA);
        const angleB = signedAngleBetweenVectors(combinedVector, vecToB);

        return angleA - angleB;
    });
}

/**
 * Determines which vector pair a point belongs to based on angular relationships
 */
function findVectorPairForPoint(pt: Point, role: Point, vectorPairs: Array<{ first: Point; second: Point }>): number {
    const pointVector = vectorFromTo(role, pt);

    // Find the vector pair that best aligns with the point
    let bestPairIndex = 0;
    let bestAlignment = Number.NEGATIVE_INFINITY;

    for (let i = 0; i < vectorPairs.length; i++) {
        const { first: vec1, second: vec2 } = vectorPairs[i];
        const combinedVector = point(vec1.x + vec2.x, vec1.y + vec2.y);

        // Calculate alignment as dot product (cosine of angle)
        const combinedMagnitude = vectorMagnitude(combinedVector);
        const pointMagnitude = vectorMagnitude(pointVector);

        const alignment =
            combinedMagnitude > DEFAULT_TOLERANCE && pointMagnitude > DEFAULT_TOLERANCE
                ? dotProduct(
                      point(combinedVector.x / combinedMagnitude, combinedVector.y / combinedMagnitude),
                      point(pointVector.x / pointMagnitude, pointVector.y / pointMagnitude)
                  )
                : 0.0;

        if (alignment > bestAlignment) {
            bestAlignment = alignment;
            bestPairIndex = i;
        }
    }

    return bestPairIndex;
}

/**
 * Calculates the intersection point of two lines defined by point and direction vector
 * Returns null if lines are parallel or don't intersect
 */
function calculateLineIntersection(p1: Point, dir1: Point, p2: Point, dir2: Point): Point | null {
    // Line 1: p1 + t * dir1
    // Line 2: p2 + s * dir2
    // Intersection: p1 + t * dir1 = p2 + s * dir2

    const det = dir1.x * dir2.y - dir1.y * dir2.x;

    // Lines are parallel if determinant is close to zero
    if (Math.abs(det) < DEFAULT_TOLERANCE) {
        return null;
    }

    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;

    const t = (dx * dir2.y - dy * dir2.x) / det;

    return point(p1.x + t * dir1.x, p1.y + t * dir1.y);
}
