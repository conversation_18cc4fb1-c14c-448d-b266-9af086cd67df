import { Line, Point, point } from '@flatten-js/core';
import { RenderCircle, RenderEllipse, RenderLine, RenderSector } from '../model';
import { GeoDocCtrl } from '../objects';
import { createFlattenCircle, createFlattenLine } from './util.flatten';
import {
    pointsByRotation,
    pointsOnParallelVector,
    pointsByRotationFor2Ellipse,
    createEllipseWithFoci,
} from './util.order';
import { Coefficients } from '../solving/coefficients';
import { cos, sin, sqrt } from '../extension.function/number.ext.func';
import { PolyBase } from '../solving/poly.solving';
import { Complex } from '../solving/complex';

// =================== TYPES & UTILITIES ===================

export function isPointInSector(pt: Point, sector: RenderSector, docCtrl: GeoDocCtrl): boolean {
    try {
        const centerCoords = sector.coord('center', docCtrl.rendererCtrl);
        const center = point(centerCoords[0], centerCoords[1]);

        const dx = pt.x - center.x;
        const dy = pt.y - center.y;
        let pointAngle = Math.atan2(dy, dx);
        if (pointAngle < 0) pointAngle += 2 * Math.PI;

        const startCoords = sector.coord('start', docCtrl.rendererCtrl);
        const endCoords = sector.coord('end', docCtrl.rendererCtrl);
        if (!startCoords || !endCoords) return true;

        const startDx = startCoords[0] - center.x;
        const startDy = startCoords[1] - center.y;
        let startAngle = Math.atan2(startDy, startDx);
        if (startAngle < 0) startAngle += 2 * Math.PI;

        const endDx = endCoords[0] - center.x;
        const endDy = endCoords[1] - center.y;
        let endAngle = Math.atan2(endDy, endDx);
        if (endAngle < 0) endAngle += 2 * Math.PI;

        return startAngle <= endAngle
            ? pointAngle >= startAngle && pointAngle <= endAngle
            : pointAngle >= startAngle || pointAngle <= endAngle;
    } catch {
        return false;
    }
}

// =================== INTERSECTION FUNCTIONS ===================

export function calculateLineLineIntersection(line1: RenderLine, line2: RenderLine, docCtrl: GeoDocCtrl) {
    const flattenLine1 = createFlattenLine(line1, docCtrl);
    const flattenLine2 = createFlattenLine(line2, docCtrl);
    return flattenLine1.intersect(flattenLine2);
}

export function calculateLineCircleIntersection(line: RenderLine, circle: RenderCircle, docCtrl: GeoDocCtrl) {
    const flattenLine = createFlattenLine(line, docCtrl);
    const flattenCircle = createFlattenCircle(circle, docCtrl);
    const intersections = flattenLine.intersect(flattenCircle);

    if (intersections.length <= 1) return intersections;

    const orderedVector = line.orderedVector(docCtrl.rendererCtrl);
    const directionVector = point(orderedVector[0], orderedVector[1]);
    return pointsOnParallelVector(directionVector, intersections);
}

export function calculateCircleCircleIntersection(circle1: RenderCircle, circle2: RenderCircle, docCtrl: GeoDocCtrl) {
    const flattenCircle1 = createFlattenCircle(circle1, docCtrl);
    const flattenCircle2 = createFlattenCircle(circle2, docCtrl);
    const intersections = flattenCircle1.intersect(flattenCircle2);

    if (intersections.length <= 1) return intersections;

    const center1 = flattenCircle1.center;
    const center2 = flattenCircle2.center;
    const connectionVector = point(center2.x - center1.x, center2.y - center1.y);
    return pointsByRotation(connectionVector, center1, intersections);
}

export function intersectionLineEllipse(line: RenderLine, ellipse: RenderEllipse, docCtrl: GeoDocCtrl): Point[] {
    const flattenLine = createFlattenLine(line, docCtrl);

    const centerCoords = ellipse.coord('center', docCtrl.rendererCtrl);
    const pC = point(centerCoords[0], centerCoords[1]);

    const intersections = intersectionLineEllipseLegacy(flattenLine, pC, ellipse.a, ellipse.b, ellipse.rotate);

    // Apply ordering if there are multiple intersections - use parallel vector ordering for line intersections
    if (intersections.length <= 1) return intersections;

    const orderedVector = line.orderedVector(docCtrl.rendererCtrl);
    const directionVector = point(orderedVector[0], orderedVector[1]);
    return pointsOnParallelVector(directionVector, intersections);
}

export function intersectionCircleEllipse(circle: RenderCircle, ellipse: RenderEllipse, docCtrl: GeoDocCtrl): Point[] {
    const centerCoords = circle.coord('center', docCtrl.rendererCtrl);
    const cC = point(centerCoords[0], centerCoords[1]);
    const cR = circle.radius;

    const ellipseCenterCoords = ellipse.coord('center', docCtrl.rendererCtrl);
    const pC = point(ellipseCenterCoords[0], ellipseCenterCoords[1]);

    const intersections = intersectionCircleEllipseLegacy(cC, cR, pC, ellipse.a, ellipse.b, ellipse.rotate);

    // Apply ordering if there are multiple intersections - use rotation ordering for circle intersections
    if (intersections.length <= 1) return intersections;

    const connectionVector = point(pC.x - cC.x, pC.y - cC.y);
    return pointsByRotation(connectionVector, cC, intersections);
}

export function intersectionEllipses(ellipse1: RenderEllipse, ellipse2: RenderEllipse, docCtrl: GeoDocCtrl): Point[] {
    const center1Coords = ellipse1.coord('center', docCtrl.rendererCtrl);
    const pC1 = point(center1Coords[0], center1Coords[1]);

    const center2Coords = ellipse2.coord('center', docCtrl.rendererCtrl);
    const pC2 = point(center2Coords[0], center2Coords[1]);

    const intersections = intersectionEllipsesLegacy(
        pC1,
        ellipse1.a,
        ellipse1.b,
        ellipse1.rotate,
        pC2,
        ellipse2.a,
        ellipse2.b,
        ellipse2.rotate
    );

    // Apply ordering if there are multiple intersections - use rotation ordering for ellipse intersections
    if (intersections.length <= 1) return intersections;

    // Create ellipse objects with focal points for the new ordering function
    const ellipse1Obj = createEllipseWithFoci(pC1, ellipse1.a, ellipse1.b, ellipse1.rotate);
    const ellipse2Obj = createEllipseWithFoci(pC2, ellipse2.a, ellipse2.b, ellipse2.rotate);

    // Use the new pointsByRotationFor2Ellipse function
    const orderedResults = pointsByRotationFor2Ellipse(ellipse1Obj, ellipse2Obj, intersections);
    return orderedResults.map(result => result.point);
}

export function intersectionEllipsesV2(
    ellipse1: RenderEllipse,
    ellipse2: RenderEllipse,
    docCtrl: GeoDocCtrl
): Array<{ point: Point; pairIndex: number }> {
    const center1Coords = ellipse1.coord('center', docCtrl.rendererCtrl);
    const pC1 = point(center1Coords[0], center1Coords[1]);

    const center2Coords = ellipse2.coord('center', docCtrl.rendererCtrl);
    const pC2 = point(center2Coords[0], center2Coords[1]);

    const intersections = intersectionEllipsesLegacy(
        pC1,
        ellipse1.a,
        ellipse1.b,
        ellipse1.rotate,
        pC2,
        ellipse2.a,
        ellipse2.b,
        ellipse2.rotate
    );

    // Create ellipse objects with focal points for the new ordering function
    const ellipse1Obj = createEllipseWithFoci(pC1, ellipse1.a, ellipse1.b, ellipse1.rotate);
    const ellipse2Obj = createEllipseWithFoci(pC2, ellipse2.a, ellipse2.b, ellipse2.rotate);

    // Use the new pointsByRotationFor2Ellipse function
    const orderedResults = pointsByRotationFor2Ellipse(ellipse1Obj, ellipse2Obj, intersections);
    return orderedResults;
}

// =================== LEGACY FUNCTIONS ===================

export function intersectionLineEllipseLegacy(
    line: Line,
    pC: Point,
    aEll: number,
    bEll: number,
    rotateRadEll: number
): Point[] {
    const h = pC.x;
    const k = pC.y;
    const A = rotateRadEll;

    const a = cos(A).pow(2) / aEll.pow(2) + sin(A).pow(2) / bEll.pow(2);
    const b = sin(A).pow(2) / aEll.pow(2) + cos(A).pow(2) / bEll.pow(2);
    const c = sin(2 * A) / aEll.pow(2) - sin(2 * A) / bEll.pow(2);
    const d =
        (-2 * h * cos(A).pow(2)) / aEll.pow(2) -
        (k * sin(2 * A)) / aEll.pow(2) -
        (2 * h * sin(A).pow(2)) / bEll.pow(2) +
        (k * sin(2 * A)) / bEll.pow(2);
    const e =
        (-h * sin(2 * A)) / aEll.pow(2) -
        (2 * k * sin(A).pow(2)) / aEll.pow(2) +
        (h * sin(2 * A)) / bEll.pow(2) -
        (2 * k * cos(A).pow(2)) / bEll.pow(2);
    const f =
        (h.pow(2) * cos(A).pow(2)) / aEll.pow(2) +
        (h * k * sin(2 * A)) / aEll.pow(2) +
        (k.pow(2) * sin(A).pow(2)) / aEll.pow(2) +
        (h.pow(2) * sin(A).pow(2)) / bEll.pow(2) -
        (h * k * sin(2 * A)) / bEll.pow(2) +
        (k.pow(2) * cos(A).pow(2)) / bEll.pow(2) -
        1;

    const n = line.norm;
    const u = n.x;
    const v = n.y;
    const m = u * line.pt.x + v * line.pt.y;

    const can = sqrt(
        e.pow(2) * u.pow(2) -
            4 * b * u.pow(2) * f +
            2 * e * c * m * u +
            4 * c * u * v * f -
            2 * e * d * u * v -
            4 * b * m * d * u +
            c.pow(2) * m.pow(2) +
            2 * c * m * d * v +
            d.pow(2) * v.pow(2) -
            4 * b * a * m.pow(2) -
            4 * a * v.pow(2) * f -
            4 * e * a * m * v
    );
    const x1 =
        (d * v.pow(2) + c * m * v - e * u * v - 2 * b * m * u + v * can) /
        (2 * (-b * u.pow(2) + c * u * v - a * v.pow(2)));
    const y1 =
        (e * u.pow(2) + c * m * u - d * u * v - 2 * a * m * v - u * can) /
        (2 * (-a * v.pow(2) + c * u * v - b * u.pow(2)));
    const x2 =
        (d * v.pow(2) + c * m * v - e * u * v - 2 * b * m * u - v * can) /
        (2 * (-b * u.pow(2) + c * u * v - a * v.pow(2)));
    const y2 =
        (e * u.pow(2) + c * m * u - d * u * v - 2 * a * m * v + u * can) /
        (2 * (-a * v.pow(2) + c * u * v - b * u.pow(2)));

    return [point(x1, y1), point(x2, y2)];
}

export function intersectionCircleEllipseLegacy(
    cC: Point,
    cR: number,
    pC: Point,
    aEll: number,
    bEll: number,
    rotateRadEll: number
): Point[] {
    return intersectionEllipsesLegacy(cC, cR, cR, 0, pC, aEll, bEll, rotateRadEll);
}

export function intersectionEllipsesLegacy(
    pC: Point,
    aEll: number,
    bEll: number,
    rotateRadEll: number,
    pC1: Point,
    aEll1: number,
    bEll1: number,
    rotateRadEll1: number
): Point[] {
    const coefs1 = _calculateImplicitFormulaCoefficients(pC.x, pC.y, aEll, bEll, rotateRadEll);
    const coefs2 = _calculateImplicitFormulaCoefficients(pC1.x, pC1.y, aEll1, bEll1, rotateRadEll1);

    // Define functions for the numerator and denominator for calculating x positions
    const numerator = (y: number): number => c * y ** 2 + e * y + f;
    const denominator = (y: number): number => b * y + d;

    // Get coefficients for implicit equation (IE) representing the intersection of the two ellipses
    // eliminating the x^2 term
    const IE = Coefficients.elliminateTerm(coefs1, coefs2, 'a');
    const { b, c, d, e, f } = IE;

    // Get the implicit coefficients for one of the two ellipses
    const { a: sa, b: sb, c: sc, d: sd, e: se, f: sf } = coefs1;

    // Calculate the coefficients for the quartic function of y
    const p = c * d + b * e;
    const q = d * e + b * f;
    const z4 = sa * c * c + sc * b * b - sb * b * c;
    const z3 = 2 * sa * c * e + 2 * sc * b * d + se * b * b - sd * b * c - sb * p;
    const z2 = sa * (2 * c * f + e * e) + sc * d * d + 2 * se * b * d + sf * b * b - sd * p - sb * q;
    const z1 = 2 * sa * e * f + se * d * d + 2 * sf * b * d - sd * q - sb * d * f;
    const z0 = sa * f * f + sf * d * d - sd * d * f;

    // Get the quartic function of y and solve it for all roots
    const quartic = PolyBase.getPoly([z4, z3, z2, z1, z0]);
    let roots = quartic.roots();

    // Remove duplicates and complex roots using custom EPSILON
    roots = Complex.removeDuplicates(roots, Complex.EPSILON);
    roots = Complex.filterRealRoots(roots, Complex.EPSILON);

    // Calculate the x position for all real roots
    const intersects: Point[] = [];
    for (const r of roots) {
        const d = denominator(r.real);

        // If denominator is near zero, solve a quadratic function in terms of x
        if (Math.abs(d) < Complex.EPSILON) {
            const a = sa;
            const b = sb * r.real + sd;
            const c = sc * r.real ** 2 + se * r.real + sf;
            let qroots = PolyBase.getPoly([a, b, c]).roots();

            qroots = Complex.removeDuplicates(qroots, Complex.EPSILON);
            qroots = Complex.filterRealRoots(qroots, Complex.EPSILON);

            for (const qr of qroots) {
                intersects.push(new Point(qr.real, r.real));
            }
        } else {
            const n = numerator(r.real);
            intersects.push(new Point(-n / d, r.real));
        }
    }

    return intersects;
}

/**
 * Calculate the coefficients for the implicit equation
 *  a.x^2 + b.x.y + c.y^2 + d.x + e.y + f = 0
 * by first rotating the ellipse then translating it.
 */
function _calculateImplicitFormulaCoefficients(
    _cx: number,
    _cy: number,
    _xr: number,
    _yr: number,
    _ang: number
): Coefficients {
    const angle = -_ang;
    const A = Math.cos(angle),
        B = Math.sin(angle);
    const cx = _cx,
        cy = _cy;
    const xr2 = _xr ** 2,
        yr2 = _yr ** 2;
    const AA = (A * A) / xr2 + (B * B) / yr2;
    const BB = (-2 * A * B) / xr2 + (2 * A * B) / yr2;
    const CC = (B * B) / xr2 + (A * A) / yr2;
    const a = AA;
    const b = BB;
    const c = CC;
    const d = -(2 * AA * cx + BB * cy);
    const e = -(BB * cx + 2 * CC * cy);
    const f = AA * cx * cx + BB * cx * cy + CC * cy * cy - 1;
    return new Coefficients(a, b, c, d, e, f);
}
