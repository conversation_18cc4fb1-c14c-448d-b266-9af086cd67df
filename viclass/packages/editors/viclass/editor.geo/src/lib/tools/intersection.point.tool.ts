import { Point } from '@flatten-js/core';
import { <PERSON>rrorH<PERSON>lerDecorator, UIPointerEventData } from '@viclass/editor.core';
import { syncPreviewCommands, syncRemovePreviewCmd } from '../cmd';
import { geoDefaultHandlerFn, GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue, pVertex } from '../model/util.preview';
import { isLargerThan } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import { RepeatSelector } from '../selectors';
import { nLines, SelectedStroke, strk } from '../selectors/common.selection';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { buildIntersectionRequest, getElementConstructionDetails } from './util.construction';
import { createExtractFlattenLine } from './util.flatten';
import {
    calculateCircleCircleIntersection,
    calculateLineCircleIntersection,
    calculateLineLineIntersection,
    intersectionCircleEllipse,
    intersectionEllipsesV2,
    intersectionLineEllipse,
    isPointInSector,
} from './util.intersections';
import { assignNames, getFocusDocCtrl, handleIfPointerNotInError, isElementLine, remoteConstruct } from './util.tool';

/**
 * Intersection Point Tool - Creates intersection points between two geometric elements
 * Follows standardized geometry tool patterns for selection, preview, and construction
 */
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    override readonly toolType: GeometryToolType = 'IntersectionPointTool';

    declare selLogic: RepeatSelector<SelectedStroke>;
    private pQ = new PreviewQueue();
    private intersectionPreview: RenderVertex[] = [];
    private intersectionConstructed: RenderVertex[] = [];
    private allIntersections: { x: number; y: number }[] = []; // All intersections including outside
    private visibleIntersections: { x: number; y: number }[] = []; // Only intersections within bounds
    private ellipseIntersectionResults: Array<{ point: Point; pairIndex: number }> = []; // For ellipse-ellipse intersections with pairIndex

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    /**
     * Creates selection logic using standardized selector pattern
     */
    private createSelLogic() {
        this.selLogic = nLines(this.pQ, this.pointerHandler.cursor, {
            count: 2,
            onComplete: this.performIntersectionPreview.bind(this),
        });
    }

    override resetState() {
        this.selLogic?.reset();
        this.intersectionPreview = [];
        this.intersectionConstructed = [];
        this.allIntersections = [];
        this.visibleIntersections = [];
        this.ellipseIntersectionResults = [];
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType === 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType === 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();
        return event;
    }

    /**
     * Handles selection attempts following standardized pattern
     */
    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        // Check if user clicked on existing intersection preview
        if (this.intersectionPreview.length > 0) {
            const hitCtx = ctrl.editor.checkHitInternal(ctrl.layers[0], event, false, true);
            const hitEl = hitCtx?.hitDetails?.el;

            if (hitEl && hitEl.type === 'RenderVertex') {
                const previewPoint = this.intersectionPreview.find(p => p.relIndex === hitEl.relIndex);
                if (previewPoint && event.eventType === 'pointerup') {
                    this.performConstruction(ctrl, previewPoint);
                    return;
                }
            }
        }

        // Try selection with the selector
        this.selLogic.trySelect(event, ctrl);
        this.pQ.flush(ctrl);
    }

    /**
     * Generates intersection preview when two elements are selected
     * Calculates both all intersections and visible intersections
     */
    private async performIntersectionPreview(selector: RepeatSelector<SelectedStroke>, docCtrl: GeoDocCtrl) {
        const selectedElements = selector.selected || [];
        if (selectedElements.length !== 2) return;

        // Clear previous previews and constructed intersections
        this.intersectionPreview = [];
        this.intersectionConstructed = [];
        this.allIntersections = [];
        this.visibleIntersections = [];
        this.ellipseIntersectionResults = [];

        const [element1, element2] = selectedElements;

        // Extract StrokeType from SelectedStroke using strk helper
        const stroke1 = strk(element1);
        const stroke2 = strk(element2);

        // Calculate all intersections first
        this.allIntersections = this.calculateIntersections(stroke1, stroke2, docCtrl) || [];
        // Filter visible intersections based on element bounds
        this.visibleIntersections = this.filterVisibleIntersections(this.allIntersections, stroke1, stroke2, docCtrl);

        if (this.visibleIntersections.length === 0) {
            this.resetState();
            return;
        }

        // Special case for line-line: construct immediately
        if (isElementLine(stroke1) && isElementLine(stroke2)) {
            if (this.visibleIntersections.length === 0) return;
            const intersectionVertex = pVertex(-9998, [this.visibleIntersections[0].x, this.visibleIntersections[0].y]);
            this.intersectionPreview.push(intersectionVertex);
            await this.performConstruction(docCtrl, intersectionVertex);
            return;
        }

        // Create preview points for other intersection types
        // Only show visible intersections in preview
        this.createIntersectionPreviews(this.visibleIntersections, docCtrl);
    }

    private createCircleFromSector(sector: RenderSector): RenderCircle {
        const circle = new RenderCircle();

        circle.centerPointIdx = sector.centerPointIdx;
        circle.radius = sector.radius;
        circle.length = 2 * Math.PI * sector.radius;

        return circle;
    }

    /**
     * Calculates intersection points between two elements
     * @param includeOutside - Whether to include intersections outside element bounds
     */
    private calculateIntersections(
        element1: StrokeType,
        element2: StrokeType,
        docCtrl: GeoDocCtrl
    ): { x: number; y: number }[] | null {
        try {
            // Line-Line intersection
            if (isElementLine(element1) && isElementLine(element2))
                return calculateLineLineIntersection(element1 as RenderLine, element2 as RenderLine, docCtrl);

            // Line-Circle intersection
            if (
                (isElementLine(element1) && element2.type === 'RenderCircle') ||
                (element1.type === 'RenderCircle' && isElementLine(element2))
            ) {
                const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
                const circle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;
                return calculateLineCircleIntersection(line, circle, docCtrl);
            }

            // Line-Ellipse intersection
            if (
                (isElementLine(element1) && element2.type === 'RenderEllipse') ||
                (element1.type === 'RenderEllipse' && isElementLine(element2))
            ) {
                const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
                const ellipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;
                return intersectionLineEllipse(line, ellipse, docCtrl);
            }

            // Circle-Circle intersection
            if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle') {
                if (!isLargerThan(element1.relIndex, element2.relIndex))
                    return calculateCircleCircleIntersection(
                        element1 as RenderCircle,
                        element2 as RenderCircle,
                        docCtrl
                    );
                else
                    return calculateCircleCircleIntersection(
                        element2 as RenderCircle,
                        element1 as RenderCircle,
                        docCtrl
                    );
            }

            // Circle-Ellipse intersection
            if (
                (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
                (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
            ) {
                const circle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;
                const ellipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;
                return intersectionCircleEllipse(circle, ellipse, docCtrl);
            }

            // Ellipse-Ellipse intersection
            if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse') {
                if (!isLargerThan(element1.relIndex, element2.relIndex)) {
                    this.ellipseIntersectionResults = intersectionEllipsesV2(
                        element1 as RenderEllipse,
                        element2 as RenderEllipse,
                        docCtrl
                    );
                } else {
                    this.ellipseIntersectionResults = intersectionEllipsesV2(
                        element2 as RenderEllipse,
                        element1 as RenderEllipse,
                        docCtrl
                    );
                }
                return this.ellipseIntersectionResults.map(result => result.point);
            }

            // Line-Sector intersection
            if (
                (isElementLine(element1) && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && isElementLine(element2))
            ) {
                const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
                const sector = (element1.type === 'RenderSector' ? element1 : element2) as RenderSector;
                return calculateLineCircleIntersection(line, this.createCircleFromSector(sector), docCtrl);
            }

            // Circle-Sector intersection
            if (
                (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
            ) {
                if (element1.type === 'RenderCircle') {
                    return calculateCircleCircleIntersection(
                        element1 as RenderCircle,
                        this.createCircleFromSector(element2 as RenderSector),
                        docCtrl
                    );
                } else {
                    return calculateCircleCircleIntersection(
                        element2 as RenderCircle,
                        this.createCircleFromSector(element1 as RenderSector),
                        docCtrl
                    );
                }
            }

            // Ellipse-Sector intersection
            if (
                (element1.type === 'RenderEllipse' && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && element2.type === 'RenderEllipse')
            ) {
                if (element1.type === 'RenderEllipse') {
                    return intersectionCircleEllipse(
                        this.createCircleFromSector(element2 as RenderSector),
                        element1 as RenderEllipse,
                        docCtrl
                    );
                } else {
                    return intersectionCircleEllipse(
                        this.createCircleFromSector(element1 as RenderSector),
                        element2 as RenderEllipse,
                        docCtrl
                    );
                }
            }

            // Sector-Sector intersection
            if (element1.type === 'RenderSector' && element2.type === 'RenderSector') {
                if (!isLargerThan(element1.relIndex, element2.relIndex))
                    return calculateCircleCircleIntersection(
                        this.createCircleFromSector(element1 as RenderSector),
                        this.createCircleFromSector(element2 as RenderSector),
                        docCtrl
                    );
                else
                    return calculateCircleCircleIntersection(
                        this.createCircleFromSector(element2 as RenderSector),
                        this.createCircleFromSector(element1 as RenderSector),
                        docCtrl
                    );
            }

            return null;
        } catch (error) {
            console.warn('Error calculating intersections:', error);
            return null;
        }
    }

    /**
     * Filters intersection points to only include those within element bounds
     * Uses isPointOn* functions to check if intersections are on the actual elements
     */
    private filterVisibleIntersections(
        intersections: { x: number; y: number }[],
        element1: StrokeType,
        element2: StrokeType,
        docCtrl: GeoDocCtrl
    ): { x: number; y: number }[] {
        if (!intersections || intersections.length === 0) return [];

        return intersections.filter(intersection => {
            const point = new Point(intersection.x, intersection.y);

            console.log(
                element1.type,
                this.isPointOnElement(point, element1, docCtrl),
                element2.type,
                this.isPointOnElement(point, element2, docCtrl)
            );

            return this.isPointOnElement(point, element1, docCtrl) && this.isPointOnElement(point, element2, docCtrl);
        });
    }

    /**
     * Checks if a point is on a specific element using the appropriate isPointOn* function
     */
    private isPointOnElement(point: Point, element: StrokeType, docCtrl: GeoDocCtrl): boolean {
        try {
            switch (element.type) {
                case 'RenderLineSegment':
                case 'RenderRay':
                case 'RenderVector':
                    return createExtractFlattenLine(element as RenderLine, docCtrl).contains(point);
                case 'RenderSector':
                    return isPointInSector(point, element as RenderSector, docCtrl);
                default:
                    return true;
            }
        } catch (error) {
            console.warn('Error checking if point is on element:', error);
            return false;
        }
    }

    /**
     * Creates preview vertices for intersection points in the correct order
     * Order matches the backend calculation to ensure nth parameter consistency
     */
    private createIntersectionPreviews(intersections: { x: number; y: number }[], docCtrl: GeoDocCtrl) {
        // Create preview vertices in the same order as intersections array
        // This ensures nth parameter calculation matches backend behavior
        intersections.forEach((intersection, index) => {
            const previewVertex = pVertex(-9998 - index, [intersection.x, intersection.y]);
            this.intersectionPreview.push(previewVertex);
            syncPreviewCommands(previewVertex, docCtrl);
        });

        // Enable filtering for multiple intersection points
        if (this.intersectionPreview.length > 1) {
            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    /**
     * Performs construction of selected intersection point
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(docCtrl: GeoDocCtrl, intersectionSelected: RenderVertex) {
        const { pcs, points } = await assignNames(
            docCtrl,
            [intersectionSelected],
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Giao Điểm'
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        const construction = this.buildConstructionRequest(intersectionSelected, points[0].name);
        if (!construction) return;

        try {
            await remoteConstruct(docCtrl, construction, [], this.editor.geoGateway, 'giao điểm');
            this.intersectionConstructed.push(intersectionSelected);

            // Update remaining previews
            this.updateRemainingPreviews(docCtrl);
        } catch (e) {
            this.resetState();
            throw e;
        }
    }

    /**
     * Builds construction request based on selected elements and intersection point
     */
    private buildConstructionRequest(
        intersectionPoint: RenderVertex,
        pointName: string
    ): GeoElConstructionRequest | undefined {
        const selectedElements = this.selLogic.selected;
        if (!selectedElements || selectedElements.length !== 2) return undefined;

        const [element1, element2] = selectedElements;

        // Extract StrokeType from SelectedStroke using strk helper
        const stroke1 = strk(element1);
        const stroke2 = strk(element2);

        const first = isLargerThan(stroke1.relIndex, stroke2.relIndex) ? stroke2 : stroke1;
        const second = first === stroke2 ? stroke1 : stroke2;

        const paramA = getElementConstructionDetails(first);
        const paramB = getElementConstructionDetails(second);

        // Determine intersection type
        const cgName = this.getIntersectionTypeName(stroke1, stroke2);
        if (!cgName) return undefined;

        // Calculate nth parameter for multiple intersections
        const currentNth = this.calculateNthParameter(intersectionPoint.relIndex);

        return buildIntersectionRequest({
            cgName,
            outputName: pointName,
            paramA,
            paramB,
            nth: cgName === 'LineLine' ? undefined : currentNth,
        });
    }

    /**
     * Gets intersection type name for construction
     */
    private getIntersectionTypeName(element1: StrokeType, element2: StrokeType): string | undefined {
        if (isElementLine(element1) && isElementLine(element2)) return 'LineLine';
        if (
            (isElementLine(element1) && element2.type === 'RenderCircle') ||
            (element1.type === 'RenderCircle' && isElementLine(element2))
        )
            return 'LineCircle';
        if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle') return 'CircleCircle';
        if (
            (isElementLine(element1) && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && isElementLine(element2))
        )
            return 'LineEllipse';
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
        )
            return 'CircleEllipse';
        if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse') return 'EllipseEllipse';
        if (
            (isElementLine(element1) && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && isElementLine(element2))
        )
            return 'LineSector';
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
        )
            return 'CircleSector';
        if (
            (element1.type === 'RenderEllipse' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderEllipse')
        )
            return 'EllipseSector';
        if (element1.type === 'RenderSector' && element2.type === 'RenderSector') return 'SectorSector';
        return undefined;
    }

    /**
     * Calculates nth parameter for intersection point selection
     * For ellipse-ellipse intersections, uses pairIndex from intersectionEllipsesV2
     * For other intersections, uses all intersections (including outside bounds) to match backend ordering
     */
    private calculateNthParameter(relIdx: number): number | undefined {
        if (this.allIntersections.length === 1) return undefined;

        // Find which visible intersection was selected
        const selectedVisibleIndex = this.intersectionPreview.findIndex(p => p.relIndex === relIdx);
        if (selectedVisibleIndex < 0) return undefined;

        const selectedVisiblePoint = this.visibleIntersections[selectedVisibleIndex];
        if (!selectedVisiblePoint) return undefined;

        // For ellipse-ellipse intersections, use pairIndex from intersectionEllipsesV2
        if (this.ellipseIntersectionResults.length > 0) {
            const ellipseResult = this.ellipseIntersectionResults.find(
                result =>
                    Math.abs(result.point.x - selectedVisiblePoint.x) < Number.EPSILON &&
                    Math.abs(result.point.y - selectedVisiblePoint.y) < Number.EPSILON
            );
            if (ellipseResult) {
                // Return pairIndex + 1 to match backend expectation (backend uses nth - 1)
                return ellipseResult.pairIndex + 1;
            }
        }

        // For other intersection types, find the position in the all intersections array
        // This ensures nth parameter matches the backend calculation order
        const allIndex = this.allIntersections.findIndex(
            pt =>
                Math.abs(pt.x - selectedVisiblePoint.x) < Number.EPSILON &&
                Math.abs(pt.y - selectedVisiblePoint.y) < Number.EPSILON
        );

        // Return 1-based index to match backend expectation (backend uses nth - 1)
        return allIndex >= 0 ? allIndex + 1 : undefined;
    }

    /**
     * Updates remaining preview points after construction
     */
    private updateRemainingPreviews(docCtrl: GeoDocCtrl) {
        if (this.intersectionPreview.length - this.intersectionConstructed.length === 0) this.resetState();
        else
            // Update remaining preview points
            this.intersectionConstructed.forEach(p => syncRemovePreviewCmd(p, docCtrl));
    }
}
